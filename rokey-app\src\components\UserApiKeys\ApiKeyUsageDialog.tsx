'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Activity, 
  Calendar, 
  Clock, 
  TrendingUp, 
  AlertCircle,
  RefreshCw,
  Download
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ApiKeyUsageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  apiKey: any;
}

export function ApiKeyUsageDialog({
  open,
  onOpenChange,
  apiKey
}: ApiKeyUsageDialogProps) {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Activity className="h-5 w-5" />
              API Key Usage - {apiKey?.key_name}
            </h2>
            <button
              onClick={() => onOpenChange(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>

          <p className="text-gray-600 mb-6">
            Detailed usage statistics and analytics for your API key
          </p>

          <div className="text-center py-8">
            <Activity className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">Usage Analytics</h3>
            <p className="text-gray-600">
              Usage analytics will be available here. This feature is coming soon.
            </p>
          </div>

          <div className="flex justify-end">
            <button
              onClick={() => onOpenChange(false)}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
