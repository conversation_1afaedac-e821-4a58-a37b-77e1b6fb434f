'use client';

import React, { useState } from 'react';
import { 
  Key, 
  Shield, 
  Clock, 
  Globe, 
  AlertTriangle,
  Copy,
  Eye,
  EyeOff,
  Plus,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface CreateApiKeyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateApiKey: (keyData: any) => Promise<any>;
  configName: string;
  creating: boolean;
  subscriptionTier: string;
}

export function CreateApiKeyDialog({
  open,
  onOpenChange,
  onCreateApiKey,
  configName,
  creating,
  subscriptionTier
}: CreateApiKeyDialogProps) {
  const [formData, setFormData] = useState({
    key_name: '',
    permissions: {
      chat: true,
      streaming: true,
      all_models: true
    },
    rate_limit_per_minute: 30,
    rate_limit_per_hour: 500,
    rate_limit_per_day: 5000,
    allowed_ips: [] as string[],
    allowed_domains: [] as string[],
    expires_at: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.key_name.trim()) {
      alert('Please enter a name for your API key');
      return;
    }

    try {
      await onCreateApiKey({
        ...formData,
        key_name: formData.key_name.trim(),
        expires_at: formData.expires_at || undefined
      });
      onOpenChange(false);
    } catch (error) {
      // Error is handled in the parent component
    }
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Create API Key</h2>
          <p className="text-gray-600 mb-6">Create a new API key for {configName}</p>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Key Name *
              </label>
              <input
                type="text"
                value={formData.key_name}
                onChange={(e) => setFormData(prev => ({ ...prev, key_name: e.target.value }))}
                placeholder="e.g., Production API Key"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <button
                type="button"
                onClick={() => onOpenChange(false)}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={creating}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
              >
                {creating ? 'Creating...' : 'Create API Key'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
