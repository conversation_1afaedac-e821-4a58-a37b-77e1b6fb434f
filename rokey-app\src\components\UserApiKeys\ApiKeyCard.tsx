'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Off,
  <PERSON>tings,
  Trash2,
  Calendar,
  Activity,
  Shield,
  Globe
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

interface ApiKeyCardProps {
  apiKey: {
    id: string;
    key_name: string;
    key_prefix: string;
    masked_key: string;
    permissions: {
      chat: boolean;
      streaming: boolean;
      all_models: boolean;
    };
    rate_limit_per_minute: number;
    rate_limit_per_hour: number;
    rate_limit_per_day: number;
    allowed_ips: string[];
    allowed_domains: string[];
    total_requests: number;
    last_used_at?: string;
    status: 'active' | 'inactive' | 'revoked' | 'expired';
    expires_at?: string;
    created_at: string;
    custom_api_configs: {
      id: string;
      name: string;
    };
  };
  onEdit: (apiKey: any) => void;
  onRevoke: (apiKeyId: string) => void;
  onViewUsage: (apiKeyId: string) => void;
}

export function ApiKeyCard({ apiKey, onEdit, onRevoke, onViewUsage }: ApiKeyCardProps) {
  const [showFullKey, setShowFullKey] = useState(false);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('API key copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy API key');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'revoked':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const isExpired = apiKey.expires_at && new Date(apiKey.expires_at) < new Date();
  const isActive = apiKey.status === 'active' && !isExpired;

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
      <div className="flex items-start justify-between mb-4">
        <div className="space-y-1">
          <h3 className="text-lg font-semibold">{apiKey.key_name}</h3>
          <p className="text-sm text-gray-600">
            Configuration: {apiKey.custom_api_configs.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(apiKey.status)}`}>
            {apiKey.status}
          </span>
          {isExpired && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border-red-200">
              Expired
            </span>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {/* API Key Display */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">API Key</label>
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
            <code className="flex-1 text-sm font-mono">
              {showFullKey ? apiKey.masked_key : `${apiKey.key_prefix}_${'*'.repeat(32)}`}
            </code>
            <button
              onClick={() => setShowFullKey(!showFullKey)}
              className="h-8 w-8 p-0 text-gray-700 hover:bg-gray-100 rounded"
            >
              {showFullKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
            <button
              onClick={() => copyToClipboard(apiKey.masked_key)}
              className="h-8 w-8 p-0 text-gray-700 hover:bg-gray-100 rounded"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Permissions */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Permissions</label>
          <div className="flex flex-wrap gap-2">
            {apiKey.permissions.chat && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-900">
                Chat Completions
              </span>
            )}
            {apiKey.permissions.streaming && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-900">
                Streaming
              </span>
            )}
            {apiKey.permissions.all_models && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-900">
                All Models
              </span>
            )}
          </div>
        </div>

        {/* Rate Limits */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">Rate Limits</label>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="font-semibold text-blue-900">{apiKey.rate_limit_per_minute}</div>
              <div className="text-blue-700">per minute</div>
            </div>
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="font-semibold text-blue-900">{apiKey.rate_limit_per_hour}</div>
              <div className="text-blue-700">per hour</div>
            </div>
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="font-semibold text-blue-900">{apiKey.rate_limit_per_day}</div>
              <div className="text-blue-700">per day</div>
            </div>
          </div>
        </div>

        {/* Security Restrictions */}
        {(apiKey.allowed_ips.length > 0 || apiKey.allowed_domains.length > 0) && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
              <Shield className="h-4 w-4" />
              Security Restrictions
            </label>
            <div className="space-y-1 text-xs">
              {apiKey.allowed_ips.length > 0 && (
                <div className="flex items-center gap-1 text-gray-600">
                  <span className="font-medium">IPs:</span>
                  <span>{apiKey.allowed_ips.join(', ')}</span>
                </div>
              )}
              {apiKey.allowed_domains.length > 0 && (
                <div className="flex items-center gap-1 text-gray-600">
                  <Globe className="h-3 w-3" />
                  <span className="font-medium">Domains:</span>
                  <span>{apiKey.allowed_domains.join(', ')}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Usage Stats */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
            <Activity className="h-4 w-4" />
            Usage Statistics
          </label>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Total Requests:</span>
              <span className="ml-2 font-semibold">{apiKey.total_requests.toLocaleString()}</span>
            </div>
            {apiKey.last_used_at && (
              <div>
                <span className="text-gray-600">Last Used:</span>
                <span className="ml-2 font-semibold">
                  {formatDistanceToNow(new Date(apiKey.last_used_at), { addSuffix: true })}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Expiration */}
        {apiKey.expires_at && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              Expiration
            </label>
            <div className="text-sm">
              <span className={`font-semibold ${isExpired ? 'text-red-600' : 'text-gray-900'}`}>
                {new Date(apiKey.expires_at).toLocaleDateString()} at{' '}
                {new Date(apiKey.expires_at).toLocaleTimeString()}
              </span>
              {!isExpired && (
                <span className="ml-2 text-gray-600">
                  ({formatDistanceToNow(new Date(apiKey.expires_at), { addSuffix: true })})
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2">
            <button
              onClick={() => onViewUsage(apiKey.id)}
              className="inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Activity className="h-3 w-3 mr-1" />
              View Usage
            </button>
            {isActive && (
              <button
                onClick={() => onEdit(apiKey)}
                className="inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <Settings className="h-3 w-3 mr-1" />
                Edit
              </button>
            )}
          </div>
          {apiKey.status !== 'revoked' && (
            <button
              onClick={() => onRevoke(apiKey.id)}
              className="inline-flex items-center px-3 py-2 text-xs font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Revoke
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
