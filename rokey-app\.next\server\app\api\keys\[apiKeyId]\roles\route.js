/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/keys/[apiKeyId]/roles/route";
exports.ids = ["app/api/keys/[apiKeyId]/roles/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_keys_apiKeyId_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/keys/[apiKeyId]/roles/route.ts */ \"(rsc)/./src/app/api/keys/[apiKeyId]/roles/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/keys/[apiKeyId]/roles/route\",\n        pathname: \"/api/keys/[apiKeyId]/roles\",\n        filename: \"route\",\n        bundlePath: \"app/api/keys/[apiKeyId]/roles/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\keys\\\\[apiKeyId]\\\\roles\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_keys_apiKeyId_roles_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/keys/[apiKeyId]/roles/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/keys/[apiKeyId]/roles/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _config_roles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/roles */ \"(rsc)/./src/config/roles.ts\");\n\n\n // For validation\n// GET /api/keys/:apiKeyId/roles\n// Lists all assigned roles for a specific API key.\nasync function GET(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { apiKeyId } = await params;\n    if (!apiKeyId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'API Key ID is required'\n        }, {\n            status: 400\n        });\n    }\n    // TODO: Milestone 13: Auth check - user owns the API key or its parent custom_config.\n    // For now, we also need the user context if we were to enrich with their custom roles by name.\n    // const { data: { user: authUser } } = await supabase.auth.getUser(); \n    try {\n        const { data, error } = await supabase.from('api_key_role_assignments').select('role_name, created_at') // Could select more if needed, like the full Role object by joining or mapping\n        .eq('api_key_id', apiKeyId);\n        if (error) {\n            console.error('Supabase error fetching role assignments:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch role assignments',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Enrich data: Attempt to get details for predefined roles.\n        // For custom roles, we'd need to fetch them based on the user who owns the config to get name/description.\n        // This GET endpoint is primarily for listing assigned role_names; the client can fetch full details if needed.\n        const enrichedData = data.map((assignment)=>{\n            const predefinedRoleDetails = (0,_config_roles__WEBPACK_IMPORTED_MODULE_2__.getRoleById)(assignment.role_name);\n            // If it's not predefined, it might be a custom role. The client has the custom roles list.\n            return {\n                ...assignment,\n                role_details: predefinedRoleDetails || {\n                    id: assignment.role_name,\n                    name: assignment.role_name,\n                    description: 'Custom role (details managed globally)'\n                }\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(enrichedData || [], {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in GET /api/keys/:apiKeyId/roles:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/keys/:apiKeyId/roles\n// Assigns a new role (predefined or user's global custom) to an API key.\nasync function POST(request, { params }) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    const { apiKeyId } = await params;\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in POST role assignment:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to assign roles.'\n        }, {\n            status: 401\n        });\n    }\n    const authenticatedUserId = session.user.id;\n    if (!apiKeyId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'API Key ID is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const { role_name } = await request.json();\n        if (!role_name || typeof role_name !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Role name (role_id) is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // Fetch API key details and the user_id of the custom_api_config owner\n        const { data: apiKeyRecord, error: apiKeyFetchError } = await supabase.from('api_keys').select(`\n        custom_api_config_id,\n        custom_api_configs ( user_id )\n      `).eq('id', apiKeyId).single();\n        if (apiKeyFetchError || !apiKeyRecord) {\n            console.error('API Key not found or error fetching details:', apiKeyFetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API Key not found or failed to fetch its details'\n            }, {\n                status: 404\n            });\n        }\n        // Ensure the fetched apiKeyRecord.custom_api_configs is not null and has a user_id\n        // Supabase typing for nested selects can be tricky, so we ensure structure.\n        const configOwnerUserId = apiKeyRecord.custom_api_configs?.user_id;\n        if (!configOwnerUserId) {\n            console.error('Could not determine the owner of the Custom API Configuration for the API Key.');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Could not determine the config owner for the API Key.'\n            }, {\n                status: 500\n            });\n        }\n        // Authorization: Check if the authenticated user owns the config to which this API key belongs.\n        // This is a critical check that should ideally be part of RLS or a reusable middleware.\n        // For now, implementing it directly here.\n        // TEMPORARY: If configOwnerUserId was not found, but it's the placeholder user, this check is effectively bypassed.\n        if (configOwnerUserId && authenticatedUserId !== configOwnerUserId) {\n            console.warn(`User ${authenticatedUserId} attempted to assign role to API key ${apiKeyId} owned by user ${configOwnerUserId}.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Forbidden. You do not own the configuration this API key belongs to.'\n            }, {\n                status: 403\n            });\n        }\n        // Validate if the role_name is valid for this user\n        const isPredefined = _config_roles__WEBPACK_IMPORTED_MODULE_2__.PREDEFINED_ROLES.some((r)=>r.id === role_name);\n        let isUserCustomRole = false;\n        if (!isPredefined) {\n            const { data: customRole, error: customRoleError } = await supabase.from('user_custom_roles').select('id').eq('user_id', authenticatedUserId) // Role must belong to the authenticated user\n            .eq('role_id', role_name) // Match by the string role_id\n            .maybeSingle(); // Use maybeSingle as it might not exist\n            if (customRoleError) {\n                console.error('Error checking for user custom role:', customRoleError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error validating role.',\n                    details: customRoleError.message\n                }, {\n                    status: 500\n                });\n            }\n            if (customRole) {\n                isUserCustomRole = true;\n            }\n        }\n        if (!isPredefined && !isUserCustomRole) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Invalid role_name: ${role_name}. Not a predefined role or a custom role you own.`\n            }, {\n                status: 400\n            });\n        }\n        // The custom_api_config_id is already available from apiKeyRecord\n        const { custom_api_config_id } = apiKeyRecord;\n        const { data: assignmentData, error: assignmentError } = await supabase.from('api_key_role_assignments').insert({\n            api_key_id: apiKeyId,\n            custom_api_config_id: custom_api_config_id,\n            role_name: role_name\n        }).select().single();\n        if (assignmentError) {\n            console.error('Supabase error assigning role:', assignmentError);\n            if (assignmentError.code === '23505') {\n                // The unique constraint `unique_api_key_role` on (api_key_id, role_name) should handle this primarily.\n                // The constraint `unique_role_per_custom_config` on (custom_api_config_id, role_name) might be too restrictive if we allow multiple keys in one config to have the same *custom* role, but it makes sense for predefined roles like 'summarizer'.\n                // Given roles are now more flexible, `unique_api_key_role` is the more important one.\n                // If `unique_role_per_custom_config` is still active and causing issues for custom roles, it might need to be re-evaluated or removed for custom roles.\n                if (assignmentError.message.includes('unique_api_key_role')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'This API key already has this role assigned.',\n                        details: assignmentError.message\n                    }, {\n                        status: 409\n                    });\n                }\n                // This constraint `unique_role_per_custom_config` was designed when roles were simpler.\n                // It means a role_name (e.g., 'translator') can only be assigned to ONE key within a single Custom API Configuration.\n                // This might still be desired behavior for predefined roles to avoid ambiguity.\n                // For custom roles, a user might want to assign their `my_special_role` to multiple keys in the same config.\n                // This needs careful thought. For now, we respect existing constraints.\n                if (assignmentError.message.includes('unique_role_per_custom_config')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.',\n                        details: assignmentError.message\n                    }, {\n                        status: 409\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.',\n                    details: assignmentError.message,\n                    code: assignmentError.code\n                }, {\n                    status: 409\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to assign role to API key',\n                details: assignmentError.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(assignmentData, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/keys/:apiKeyId/roles:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/keys/[apiKeyId]/roles/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/roles.ts":
/*!*****************************!*\
  !*** ./src/config/roles.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PREDEFINED_ROLES: () => (/* binding */ PREDEFINED_ROLES),\n/* harmony export */   getRoleById: () => (/* binding */ getRoleById),\n/* harmony export */   getRoleName: () => (/* binding */ getRoleName)\n/* harmony export */ });\nconst PREDEFINED_ROLES = [\n    {\n        id: 'general_chat',\n        name: 'General Chat',\n        description: 'Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise.'\n    },\n    {\n        id: 'logic_reasoning',\n        name: 'Logic & Reasoning',\n        description: 'Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking.'\n    },\n    {\n        id: 'writing',\n        name: 'Writing & Content Creation',\n        description: 'Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives.'\n    },\n    {\n        id: 'coding_frontend',\n        name: 'Coding - Frontend',\n        description: 'Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development.'\n    },\n    {\n        id: 'coding_backend',\n        name: 'Coding - Backend',\n        description: 'Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture.'\n    },\n    {\n        id: 'research_synthesis',\n        name: 'Research & Synthesis',\n        description: 'Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports.'\n    },\n    {\n        id: 'summarization_briefing',\n        name: 'Summarization & Briefing',\n        description: 'Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information.'\n    },\n    {\n        id: 'translation_localization',\n        name: 'Translation & Localization',\n        description: 'Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication.'\n    },\n    {\n        id: 'data_extraction_structuring',\n        name: 'Data Extraction & Structuring',\n        description: 'Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats.'\n    },\n    {\n        id: 'brainstorming_ideation',\n        name: 'Brainstorming & Ideation',\n        description: 'Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions.'\n    },\n    {\n        id: 'education_tutoring',\n        name: 'Education & Tutoring',\n        description: 'Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance.'\n    },\n    {\n        id: 'image_generation',\n        name: 'Image Generation',\n        description: 'Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models.'\n    },\n    {\n        id: 'audio_transcription',\n        name: 'Audio Transcription',\n        description: 'Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing.'\n    },\n    {\n        id: 'data_extractor',\n        name: 'Data Extractor',\n        description: 'Extracting specific data from web pages, scraping content, and gathering information from websites.'\n    },\n    {\n        id: 'form_filler',\n        name: 'Form Filler',\n        description: 'Filling out web forms, submitting data, and handling form-based interactions on websites.'\n    },\n    {\n        id: 'verification_agent',\n        name: 'Verification Agent',\n        description: 'Verifying information on websites, fact-checking, and validating data accuracy.'\n    },\n    {\n        id: 'research_assistant',\n        name: 'Research Assistant',\n        description: 'Conducting web-based research, gathering information from multiple sources, and compiling research findings.'\n    },\n    {\n        id: 'shopping_assistant',\n        name: 'Shopping Assistant',\n        description: 'Helping with online shopping, price comparisons, product research, and e-commerce tasks.'\n    },\n    {\n        id: 'price_comparison',\n        name: 'Price Comparison',\n        description: 'Comparing prices across different websites, finding deals, and analyzing product pricing.'\n    },\n    {\n        id: 'fact_checker',\n        name: 'Fact Checker',\n        description: 'Verifying facts and information across multiple web sources, cross-referencing data for accuracy.'\n    },\n    {\n        id: 'task_executor',\n        name: 'Task Executor',\n        description: 'General task execution and automation, handling various web-based tasks and workflows.'\n    }\n];\nconst getRoleById = (id)=>{\n    return PREDEFINED_ROLES.find((role)=>role.id === id);\n};\nconst getRoleName = (id)=>{\n    return PREDEFINED_ROLES.find((role)=>role.id === id)?.name;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/roles.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&page=%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkeys%2F%5BapiKeyId%5D%2Froles%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();