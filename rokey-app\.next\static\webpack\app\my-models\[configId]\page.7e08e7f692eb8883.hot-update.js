"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx":
/*!***********************************************************!*\
  !*** ./src/components/UserApiKeys/CreateApiKeyDialog.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateApiKeyDialog: () => (/* binding */ CreateApiKeyDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ CreateApiKeyDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CreateApiKeyDialog(param) {\n    let { open, onOpenChange, onCreateApiKey, configName, creating, subscriptionTier } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key_name: '',\n        permissions: {\n            chat: true,\n            streaming: true,\n            all_models: true\n        },\n        rate_limit_per_minute: 30,\n        rate_limit_per_hour: 500,\n        rate_limit_per_day: 5000,\n        allowed_ips: [],\n        allowed_domains: [],\n        expires_at: ''\n    });\n    function getDefaultRateLimits(tier) {\n        const limits = {\n            starter: {\n                per_minute: 30,\n                per_hour: 500,\n                per_day: 5000\n            },\n            professional: {\n                per_minute: 100,\n                per_hour: 2000,\n                per_day: 20000\n            },\n            enterprise: {\n                per_minute: 300,\n                per_hour: 10000,\n                per_day: 100000\n            }\n        };\n        return limits[tier] || limits.starter;\n    }\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.key_name.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please enter a name for your API key');\n            return;\n        }\n        try {\n            const result = await onCreateApiKey({\n                ...formData,\n                key_name: formData.key_name.trim(),\n                expires_at: formData.expires_at || undefined\n            });\n            setCreatedApiKey(result);\n            setStep('success');\n        } catch (error) {\n        // Error is handled in the parent component\n        }\n    };\n    const addIpAddress = ()=>{\n        if (ipInput.trim() && !formData.allowed_ips.includes(ipInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_ips: [\n                        ...prev.allowed_ips,\n                        ipInput.trim()\n                    ]\n                }));\n            setIpInput('');\n        }\n    };\n    const removeIpAddress = (ip)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_ips: prev.allowed_ips.filter((i)=>i !== ip)\n            }));\n    };\n    const addDomain = ()=>{\n        if (domainInput.trim() && !formData.allowed_domains.includes(domainInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_domains: [\n                        ...prev.allowed_domains,\n                        domainInput.trim()\n                    ]\n                }));\n            setDomainInput('');\n        }\n    };\n    const removeDomain = (domain)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_domains: prev.allowed_domains.filter((d)=>d !== domain)\n            }));\n    };\n    const copyApiKey = async ()=>{\n        var _createdApiKey;\n        if ((_createdApiKey = createdApiKey) === null || _createdApiKey === void 0 ? void 0 : _createdApiKey.api_key) {\n            try {\n                await navigator.clipboard.writeText(createdApiKey.api_key);\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key copied to clipboard');\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to copy API key');\n            }\n        }\n    };\n    const handleClose = ()=>{\n        setStep('form');\n        setCreatedApiKey(null);\n        setShowFullKey(false);\n        setFormData({\n            key_name: '',\n            permissions: {\n                chat: true,\n                streaming: true,\n                all_models: true\n            },\n            rate_limit_per_minute: getDefaultRateLimits(subscriptionTier).per_minute,\n            rate_limit_per_hour: getDefaultRateLimits(subscriptionTier).per_hour,\n            rate_limit_per_day: getDefaultRateLimits(subscriptionTier).per_day,\n            allowed_ips: [],\n            allowed_domains: [],\n            expires_at: ''\n        });\n        setIpInput('');\n        setDomainInput('');\n        onOpenChange(false);\n    };\n    if (step === 'success' && createdApiKey) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n            open: open,\n            onOpenChange: handleClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                className: \"max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"API Key Created Successfully\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogDescription, {\n                                children: \"Your API key has been created. Make sure to copy it now as you won't be able to see it again.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                className: \"border-amber-200 bg-amber-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-amber-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertDescription, {\n                                        className: \"text-amber-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" This is the only time you'll see the full API key. Make sure to copy and store it securely.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        children: \"API Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"flex-1 text-sm font-mono\",\n                                                children: showFullKey ? createdApiKey.api_key : \"\".concat(createdApiKey.key_prefix, \"_\").concat('*'.repeat(32))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowFullKey(!showFullKey),\n                                                className: \"h-8 w-8 p-0\",\n                                                children: showFullKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 34\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 67\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: copyApiKey,\n                                                className: \"h-8 w-8 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                children: \"Key Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: createdApiKey.key_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                children: \"Created\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: new Date(createdApiKey.created_at).toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        children: \"Rate Limits\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-2 bg-blue-50 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-blue-900\",\n                                                        children: createdApiKey.rate_limits.per_minute\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-700\",\n                                                        children: \"per minute\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-2 bg-blue-50 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-blue-900\",\n                                                        children: createdApiKey.rate_limits.per_hour\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-700\",\n                                                        children: \"per hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-2 bg-blue-50 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-blue-900\",\n                                                        children: createdApiKey.rate_limits.per_day\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-700\",\n                                                        children: \"per day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogFooter, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                            onClick: handleClose,\n                            className: \"w-full\",\n                            children: \"Done\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n        open: open,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                \"Create API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogDescription, {\n                            children: [\n                                \"Create a new API key for programmatic access to \",\n                                configName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        htmlFor: \"key_name\",\n                                        children: \"API Key Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                        id: \"key_name\",\n                                        value: formData.key_name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    key_name: e.target.value\n                                                })),\n                                        placeholder: \"e.g., Production API Key\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"A descriptive name to help you identify this API key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Checkbox, {\n                                                    id: \"chat\",\n                                                    checked: formData.permissions.chat,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    chat: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"chat\",\n                                                    className: \"text-sm\",\n                                                    children: \"Chat Completions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: \"Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Checkbox, {\n                                                    id: \"streaming\",\n                                                    checked: formData.permissions.streaming,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    streaming: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"streaming\",\n                                                    className: \"text-sm\",\n                                                    children: \"Streaming Responses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Checkbox, {\n                                                    id: \"all_models\",\n                                                    checked: formData.permissions.all_models,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    all_models: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"all_models\",\n                                                    className: \"text-sm\",\n                                                    children: \"Access to All Models\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Rate Limits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                subscriptionTier,\n                                                \" plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"rate_minute\",\n                                                    children: \"Per Minute\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    id: \"rate_minute\",\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"1000\",\n                                                    value: formData.rate_limit_per_minute,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                rate_limit_per_minute: parseInt(e.target.value) || 0\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"rate_hour\",\n                                                    children: \"Per Hour\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    id: \"rate_hour\",\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"50000\",\n                                                    value: formData.rate_limit_per_hour,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                rate_limit_per_hour: parseInt(e.target.value) || 0\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"rate_day\",\n                                                    children: \"Per Day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    id: \"rate_day\",\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"500000\",\n                                                    value: formData.rate_limit_per_day,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                rate_limit_per_day: parseInt(e.target.value) || 0\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Security Restrictions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: \"Optional\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            children: \"Allowed IP Addresses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    value: ipInput,\n                                                    onChange: (e)=>setIpInput(e.target.value),\n                                                    placeholder: \"e.g., *********** or 10.0.0.0/8\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addIpAddress())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    type: \"button\",\n                                                    onClick: addIpAddress,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_ips.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_ips.map((ip)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        ip,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeIpAddress(ip),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, ip, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: \"Leave empty to allow all IP addresses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            children: \"Allowed Domains (CORS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    value: domainInput,\n                                                    onChange: (e)=>setDomainInput(e.target.value),\n                                                    placeholder: \"e.g., example.com or *.example.com\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addDomain())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    type: \"button\",\n                                                    onClick: addDomain,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_domains.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        domain,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeDomain(domain),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, domain, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: \"Leave empty to allow all domains\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        htmlFor: \"expires_at\",\n                                        children: \"Expiration Date (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                        id: \"expires_at\",\n                                        type: \"datetime-local\",\n                                        value: formData.expires_at,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    expires_at: e.target.value\n                                                })),\n                                        min: new Date().toISOString().slice(0, 16)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"Leave empty for no expiration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: handleClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                    type: \"submit\",\n                                    disabled: creating,\n                                    children: creating ? 'Creating...' : 'Create API Key'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateApiKeyDialog, \"28GqZBRRZzCoUYzdhzO6jDpwRjw=\");\n_c = CreateApiKeyDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateApiKeyDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\n"));

/***/ })

});