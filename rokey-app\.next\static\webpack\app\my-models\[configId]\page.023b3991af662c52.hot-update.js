"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUsageDialog, setShowUsageDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedApiKey, setSelectedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch subscription info and API keys\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchSubscriptionInfo();\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    const fetchSubscriptionInfo = async ()=>{\n        try {\n            const response = await fetch('/api/stripe/subscription-status');\n            if (response.ok) {\n                var _data_subscription, _data_subscription1, _data_subscription2;\n                const data = await response.json();\n                // Determine key limits based on subscription tier\n                let keyLimit = 3; // Default for starter\n                if (((_data_subscription = data.subscription) === null || _data_subscription === void 0 ? void 0 : _data_subscription.tier) === 'professional') {\n                    keyLimit = 10;\n                } else if (((_data_subscription1 = data.subscription) === null || _data_subscription1 === void 0 ? void 0 : _data_subscription1.tier) === 'enterprise') {\n                    keyLimit = 25;\n                }\n                setSubscriptionInfo({\n                    tier: ((_data_subscription2 = data.subscription) === null || _data_subscription2 === void 0 ? void 0 : _data_subscription2.tier) || 'starter',\n                    keyLimit,\n                    currentCount: 0 // Will be updated when we fetch API keys\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n        }\n    };\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?custom_config_id=\".concat(configId));\n            if (response.ok) {\n                const data = await response.json();\n                setApiKeys(data.apiKeys || []);\n                // Update subscription info with current count\n                if (subscriptionInfo) {\n                    setSubscriptionInfo((prev)=>{\n                        var _data_apiKeys;\n                        return prev ? {\n                            ...prev,\n                            currentCount: ((_data_apiKeys = data.apiKeys) === null || _data_apiKeys === void 0 ? void 0 : _data_apiKeys.length) || 0\n                        } : null;\n                    });\n                }\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to fetch API keys');\n            }\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to fetch API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    custom_config_id: configId,\n                    key_name: keyData.name,\n                    permissions: keyData.permissions,\n                    rate_limit_per_minute: keyData.rateLimits.perMinute,\n                    rate_limit_per_hour: keyData.rateLimits.perHour,\n                    rate_limit_per_day: keyData.rateLimits.perDay,\n                    allowed_ips: keyData.restrictions.allowedIps,\n                    allowed_domains: keyData.restrictions.allowedDomains,\n                    expires_at: keyData.expiresAt\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key created successfully!');\n                // Show the generated key to the user (this is the only time they'll see it)\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Your API key: \".concat(data.generatedKey), {\n                    duration: 10000,\n                    description: 'Save this key now - you won\\'t be able to see it again!'\n                });\n                setShowCreateDialog(false);\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to create API key');\n            }\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to create API key');\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleEditApiKey = async (apiKeyId, updates)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(apiKeyId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key updated successfully!');\n                setShowEditDialog(false);\n                setSelectedApiKey(null);\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to update API key');\n            }\n        } catch (error) {\n            console.error('Error updating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to update API key');\n        }\n    };\n    const handleRevokeApiKey = async (apiKeyId)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(apiKeyId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key revoked successfully!');\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to revoke API key');\n            }\n        } catch (error) {\n            console.error('Error revoking API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to revoke API key');\n        }\n    };\n    const handleViewUsage = (apiKeyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === apiKeyId);\n        if (apiKey) {\n            setSelectedApiKey(apiKey);\n            setShowUsageDialog(true);\n        }\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"API Key Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Create and manage API keys for programmatic access to your RouKey configuration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"API Key Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Create and manage API keys for programmatic access to your \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: configName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 72\n                                    }, this),\n                                    \" configuration.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                        onClick: ()=>setShowCreateDialog(true),\n                        disabled: !canCreateMoreKeys,\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertDescription, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: subscriptionInfo.tier.charAt(0).toUpperCase() + subscriptionInfo.tier.slice(1)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" plan:\",\n                                        ' ',\n                                        subscriptionInfo.currentCount,\n                                        \" of \",\n                                        subscriptionInfo.keyLimit,\n                                        \" API keys used\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: fetchApiKeys,\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            disabled: !canCreateMoreKeys,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_3__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_4__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: setShowCreateDialog,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_6__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"Uxp2HmcmoyLM/7rsP3ld9j5jvUo=\");\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});