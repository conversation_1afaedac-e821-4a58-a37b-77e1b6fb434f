'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Settings,
  Shield,
  Clock,
  Globe,
  Plus,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface EditApiKeyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  apiKey: any;
  onUpdateApiKey: (updates: any) => Promise<void>;
}

export function EditApiKeyDialog({
  open,
  onOpenChange,
  apiKey,
  onUpdateApiKey
}: EditApiKeyDialogProps) {
  const [updating, setUpdating] = useState(false);
  const [formData, setFormData] = useState({
    key_name: '',
    permissions: {
      chat: true,
      streaming: true,
      all_models: true
    },
    rate_limit_per_minute: 30,
    rate_limit_per_hour: 500,
    rate_limit_per_day: 5000,
    allowed_ips: [] as string[],
    allowed_domains: [] as string[],
    status: 'active' as 'active' | 'inactive',
    expires_at: ''
  });

  const [ipInput, setIpInput] = useState('');
  const [domainInput, setDomainInput] = useState('');

  // Initialize form data when apiKey changes
  useEffect(() => {
    if (apiKey) {
      setFormData({
        key_name: apiKey.key_name || '',
        permissions: apiKey.permissions || {
          chat: true,
          streaming: true,
          all_models: true
        },
        rate_limit_per_minute: apiKey.rate_limit_per_minute || 30,
        rate_limit_per_hour: apiKey.rate_limit_per_hour || 500,
        rate_limit_per_day: apiKey.rate_limit_per_day || 5000,
        allowed_ips: apiKey.allowed_ips || [],
        allowed_domains: apiKey.allowed_domains || [],
        status: apiKey.status || 'active',
        expires_at: apiKey.expires_at ? new Date(apiKey.expires_at).toISOString().slice(0, 16) : ''
      });
    }
  }, [apiKey]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.key_name.trim()) {
      toast.error('Please enter a name for your API key');
      return;
    }

    try {
      setUpdating(true);
      await onUpdateApiKey({
        ...formData,
        key_name: formData.key_name.trim(),
        expires_at: formData.expires_at || null
      });
    } catch (error) {
      // Error is handled in the parent component
    } finally {
      setUpdating(false);
    }
  };

  const addIpAddress = () => {
    if (ipInput.trim() && !formData.allowed_ips.includes(ipInput.trim())) {
      setFormData(prev => ({
        ...prev,
        allowed_ips: [...prev.allowed_ips, ipInput.trim()]
      }));
      setIpInput('');
    }
  };

  const removeIpAddress = (ip: string) => {
    setFormData(prev => ({
      ...prev,
      allowed_ips: prev.allowed_ips.filter(i => i !== ip)
    }));
  };

  const addDomain = () => {
    if (domainInput.trim() && !formData.allowed_domains.includes(domainInput.trim())) {
      setFormData(prev => ({
        ...prev,
        allowed_domains: [...prev.allowed_domains, domainInput.trim()]
      }));
      setDomainInput('');
    }
  };

  const removeDomain = (domain: string) => {
    setFormData(prev => ({
      ...prev,
      allowed_domains: prev.allowed_domains.filter(d => d !== domain)
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Edit API Key
          </DialogTitle>
          <DialogDescription>
            Update the settings for your API key
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="key_name">API Key Name *</Label>
              <Input
                id="key_name"
                value={formData.key_name}
                onChange={(e) => setFormData(prev => ({ ...prev, key_name: e.target.value }))}
                placeholder="e.g., Production API Key"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as 'active' | 'inactive' }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <Separator />

          {/* Permissions */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <Label className="text-base font-semibold">Permissions</Label>
            </div>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="chat"
                  checked={formData.permissions.chat}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({
                      ...prev,
                      permissions: { ...prev.permissions, chat: !!checked }
                    }))
                  }
                />
                <Label htmlFor="chat" className="text-sm">
                  Chat Completions
                </Label>
                <Badge variant="secondary" className="text-xs">Required</Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="streaming"
                  checked={formData.permissions.streaming}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({
                      ...prev,
                      permissions: { ...prev.permissions, streaming: !!checked }
                    }))
                  }
                />
                <Label htmlFor="streaming" className="text-sm">
                  Streaming Responses
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="all_models"
                  checked={formData.permissions.all_models}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({
                      ...prev,
                      permissions: { ...prev.permissions, all_models: !!checked }
                    }))
                  }
                />
                <Label htmlFor="all_models" className="text-sm">
                  Access to All Models
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Rate Limits */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <Label className="text-base font-semibold">Rate Limits</Label>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="rate_minute">Per Minute</Label>
                <Input
                  id="rate_minute"
                  type="number"
                  min="1"
                  max="1000"
                  value={formData.rate_limit_per_minute}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    rate_limit_per_minute: parseInt(e.target.value) || 0 
                  }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rate_hour">Per Hour</Label>
                <Input
                  id="rate_hour"
                  type="number"
                  min="1"
                  max="50000"
                  value={formData.rate_limit_per_hour}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    rate_limit_per_hour: parseInt(e.target.value) || 0 
                  }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rate_day">Per Day</Label>
                <Input
                  id="rate_day"
                  type="number"
                  min="1"
                  max="500000"
                  value={formData.rate_limit_per_day}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    rate_limit_per_day: parseInt(e.target.value) || 0 
                  }))}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Security Restrictions */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <Label className="text-base font-semibold">Security Restrictions</Label>
            </div>

            {/* IP Restrictions */}
            <div className="space-y-2">
              <Label>Allowed IP Addresses</Label>
              <div className="flex gap-2">
                <Input
                  value={ipInput}
                  onChange={(e) => setIpInput(e.target.value)}
                  placeholder="e.g., *********** or 10.0.0.0/8"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addIpAddress())}
                />
                <Button type="button" onClick={addIpAddress} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.allowed_ips.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.allowed_ips.map((ip) => (
                    <Badge key={ip} variant="secondary" className="text-xs">
                      {ip}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeIpAddress(ip)}
                        className="ml-1 h-3 w-3 p-0"
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Domain Restrictions */}
            <div className="space-y-2">
              <Label>Allowed Domains (CORS)</Label>
              <div className="flex gap-2">
                <Input
                  value={domainInput}
                  onChange={(e) => setDomainInput(e.target.value)}
                  placeholder="e.g., example.com or *.example.com"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDomain())}
                />
                <Button type="button" onClick={addDomain} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.allowed_domains.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.allowed_domains.map((domain) => (
                    <Badge key={domain} variant="secondary" className="text-xs">
                      <Globe className="h-3 w-3 mr-1" />
                      {domain}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDomain(domain)}
                        className="ml-1 h-3 w-3 p-0"
                      >
                        <X className="h-2 w-2" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Expiration */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="expires_at">Expiration Date (Optional)</Label>
              <Input
                id="expires_at"
                type="datetime-local"
                value={formData.expires_at}
                onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                min={new Date().toISOString().slice(0, 16)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={updating}>
              {updating ? 'Updating...' : 'Update API Key'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
