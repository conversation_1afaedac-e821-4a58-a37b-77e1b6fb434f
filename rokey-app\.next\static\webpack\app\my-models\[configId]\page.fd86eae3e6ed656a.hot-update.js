"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUsageDialog, setShowUsageDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedApiKey, setSelectedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch subscription info and API keys\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchSubscriptionInfo();\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    const fetchSubscriptionInfo = async ()=>{\n        try {\n            const response = await fetch('/api/stripe/subscription-status');\n            if (response.ok) {\n                var _data_subscription, _data_subscription1, _data_subscription2;\n                const data = await response.json();\n                // Determine key limits based on subscription tier\n                let keyLimit = 3; // Default for starter\n                if (((_data_subscription = data.subscription) === null || _data_subscription === void 0 ? void 0 : _data_subscription.tier) === 'professional') {\n                    keyLimit = 10;\n                } else if (((_data_subscription1 = data.subscription) === null || _data_subscription1 === void 0 ? void 0 : _data_subscription1.tier) === 'enterprise') {\n                    keyLimit = 25;\n                }\n                setSubscriptionInfo({\n                    tier: ((_data_subscription2 = data.subscription) === null || _data_subscription2 === void 0 ? void 0 : _data_subscription2.tier) || 'starter',\n                    keyLimit,\n                    currentCount: 0 // Will be updated when we fetch API keys\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n        }\n    };\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?custom_config_id=\".concat(configId));\n            if (response.ok) {\n                const data = await response.json();\n                setApiKeys(data.apiKeys || []);\n                // Update subscription info with current count\n                if (subscriptionInfo) {\n                    setSubscriptionInfo((prev)=>{\n                        var _data_apiKeys;\n                        return prev ? {\n                            ...prev,\n                            currentCount: ((_data_apiKeys = data.apiKeys) === null || _data_apiKeys === void 0 ? void 0 : _data_apiKeys.length) || 0\n                        } : null;\n                    });\n                }\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to fetch API keys');\n            }\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to fetch API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    custom_config_id: configId,\n                    key_name: keyData.name,\n                    permissions: keyData.permissions,\n                    rate_limit_per_minute: keyData.rateLimits.perMinute,\n                    rate_limit_per_hour: keyData.rateLimits.perHour,\n                    rate_limit_per_day: keyData.rateLimits.perDay,\n                    allowed_ips: keyData.restrictions.allowedIps,\n                    allowed_domains: keyData.restrictions.allowedDomains,\n                    expires_at: keyData.expiresAt\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key created successfully!');\n                // Show the generated key to the user (this is the only time they'll see it)\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Your API key: \".concat(data.generatedKey), {\n                    duration: 10000,\n                    description: 'Save this key now - you won\\'t be able to see it again!'\n                });\n                setShowCreateDialog(false);\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to create API key');\n            }\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to create API key');\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleEditApiKey = async (apiKeyId, updates)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(apiKeyId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key updated successfully!');\n                setShowEditDialog(false);\n                setSelectedApiKey(null);\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to update API key');\n            }\n        } catch (error) {\n            console.error('Error updating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to update API key');\n        }\n    };\n    const handleRevokeApiKey = async (apiKeyId)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(apiKeyId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key revoked successfully!');\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to revoke API key');\n            }\n        } catch (error) {\n            console.error('Error revoking API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to revoke API key');\n        }\n    };\n    const handleViewUsage = (apiKeyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === apiKeyId);\n        if (apiKey) {\n            setSelectedApiKey(apiKey);\n            setShowUsageDialog(true);\n        }\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"API Key Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Create and manage API keys for programmatic access to your RouKey configuration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-gray-200 shadow-sm animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"API Key Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Create and manage API keys for programmatic access to your \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: configName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 72\n                                    }, this),\n                                    \" configuration.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCreateDialog(true),\n                        disabled: !canCreateMoreKeys,\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-900\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: subscriptionInfo.tier.charAt(0).toUpperCase() + subscriptionInfo.tier.slice(1)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" plan:\",\n                                        ' ',\n                                        subscriptionInfo.currentCount,\n                                        \" of \",\n                                        subscriptionInfo.keyLimit,\n                                        \" API keys used\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchApiKeys,\n                            className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowCreateDialog(true),\n                            disabled: !canCreateMoreKeys,\n                            className: \"inline-flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_3__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_4__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: setShowCreateDialog,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_6__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"Uxp2HmcmoyLM/7rsP3ld9j5jvUo=\");\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1VzZXJBcGlLZXlzL0FwaUtleU1hbmFnZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUNjO0FBQ2xDO0FBQ1c7QUFDZ0I7QUFDSjtBQUNFO0FBT2pELFNBQVNZLGNBQWMsS0FBNEM7UUFBNUMsRUFBRUMsUUFBUSxFQUFFQyxVQUFVLEVBQXNCLEdBQTVDOztJQUM1QixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQVEsRUFBRTtJQUNoRCxNQUFNLENBQUNnQixTQUFTQyxXQUFXLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNrQixVQUFVQyxZQUFZLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNvQixrQkFBa0JDLG9CQUFvQixHQUFHckIsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDc0IsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3dCLGlCQUFpQkMsbUJBQW1CLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMwQixnQkFBZ0JDLGtCQUFrQixHQUFHM0IsK0NBQVFBLENBQU07SUFDMUQsTUFBTSxDQUFDNEIsa0JBQWtCQyxvQkFBb0IsR0FBRzdCLCtDQUFRQSxDQUk5QztJQUVWLHVDQUF1QztJQUN2Q0MsZ0RBQVNBO21DQUFDO1lBQ1I2QjtZQUNBQztRQUNGO2tDQUFHO1FBQUNuQjtLQUFTO0lBRWIsTUFBTWtCLHdCQUF3QjtRQUM1QixJQUFJO1lBQ0YsTUFBTUUsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUlELFNBQVNFLEVBQUUsRUFBRTtvQkFLWEMsb0JBRU9BLHFCQUtIQTtnQkFYUixNQUFNQSxPQUFPLE1BQU1ILFNBQVNJLElBQUk7Z0JBRWhDLGtEQUFrRDtnQkFDbEQsSUFBSUMsV0FBVyxHQUFHLHNCQUFzQjtnQkFDeEMsSUFBSUYsRUFBQUEscUJBQUFBLEtBQUtHLFlBQVksY0FBakJILHlDQUFBQSxtQkFBbUJJLElBQUksTUFBSyxnQkFBZ0I7b0JBQzlDRixXQUFXO2dCQUNiLE9BQU8sSUFBSUYsRUFBQUEsc0JBQUFBLEtBQUtHLFlBQVksY0FBakJILDBDQUFBQSxvQkFBbUJJLElBQUksTUFBSyxjQUFjO29CQUNuREYsV0FBVztnQkFDYjtnQkFFQVIsb0JBQW9CO29CQUNsQlUsTUFBTUosRUFBQUEsc0JBQUFBLEtBQUtHLFlBQVksY0FBakJILDBDQUFBQSxvQkFBbUJJLElBQUksS0FBSTtvQkFDakNGO29CQUNBRyxjQUFjLEVBQUUseUNBQXlDO2dCQUMzRDtZQUNGO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQ0FBcUNBO1FBQ3JEO0lBQ0Y7SUFFQSxNQUFNVixlQUFlO1FBQ25CLElBQUk7WUFDRmQsV0FBVztZQUNYLE1BQU1lLFdBQVcsTUFBTUMsTUFBTSx1Q0FBZ0QsT0FBVHJCO1lBRXBFLElBQUlvQixTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQ3JCLFdBQVdvQixLQUFLckIsT0FBTyxJQUFJLEVBQUU7Z0JBRTdCLDhDQUE4QztnQkFDOUMsSUFBSWMsa0JBQWtCO29CQUNwQkMsb0JBQW9CYyxDQUFBQTs0QkFFSlI7K0JBRllRLE9BQU87NEJBQ2pDLEdBQUdBLElBQUk7NEJBQ1BILGNBQWNMLEVBQUFBLGdCQUFBQSxLQUFLckIsT0FBTyxjQUFacUIsb0NBQUFBLGNBQWNTLE1BQU0sS0FBSTt3QkFDeEMsSUFBSTs7Z0JBQ047WUFDRixPQUFPO2dCQUNMLE1BQU1DLFlBQVksTUFBTWIsU0FBU0ksSUFBSTtnQkFDckM5Qix5Q0FBS0EsQ0FBQ21DLEtBQUssQ0FBQ0ksVUFBVUosS0FBSyxJQUFJO1lBQ2pDO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDbkMseUNBQUtBLENBQUNtQyxLQUFLLENBQUM7UUFDZCxTQUFVO1lBQ1J4QixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU02QixxQkFBcUIsT0FBT0M7UUFrQmhDLElBQUk7WUFDRjVCLFlBQVk7WUFFWixNQUFNYSxXQUFXLE1BQU1DLE1BQU0sc0JBQXNCO2dCQUNqRGUsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxrQkFBa0J6QztvQkFDbEIwQyxVQUFVUCxRQUFRUSxJQUFJO29CQUN0QkMsYUFBYVQsUUFBUVMsV0FBVztvQkFDaENDLHVCQUF1QlYsUUFBUVcsVUFBVSxDQUFDQyxTQUFTO29CQUNuREMscUJBQXFCYixRQUFRVyxVQUFVLENBQUNHLE9BQU87b0JBQy9DQyxvQkFBb0JmLFFBQVFXLFVBQVUsQ0FBQ0ssTUFBTTtvQkFDN0NDLGFBQWFqQixRQUFRa0IsWUFBWSxDQUFDQyxVQUFVO29CQUM1Q0MsaUJBQWlCcEIsUUFBUWtCLFlBQVksQ0FBQ0csY0FBYztvQkFDcERDLFlBQVl0QixRQUFRdUIsU0FBUztnQkFDL0I7WUFDRjtZQUVBLElBQUl0QyxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQzlCLHlDQUFLQSxDQUFDaUUsT0FBTyxDQUFDO2dCQUVkLDRFQUE0RTtnQkFDNUVqRSx5Q0FBS0EsQ0FBQ2lFLE9BQU8sQ0FBQyxpQkFBbUMsT0FBbEJwQyxLQUFLcUMsWUFBWSxHQUFJO29CQUNsREMsVUFBVTtvQkFDVkMsYUFBYTtnQkFDZjtnQkFFQXJELG9CQUFvQjtnQkFDcEJVLGdCQUFnQixtQkFBbUI7WUFDckMsT0FBTztnQkFDTCxNQUFNYyxZQUFZLE1BQU1iLFNBQVNJLElBQUk7Z0JBQ3JDOUIseUNBQUtBLENBQUNtQyxLQUFLLENBQUNJLFVBQVVKLEtBQUssSUFBSTtZQUNqQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q25DLHlDQUFLQSxDQUFDbUMsS0FBSyxDQUFDO1FBQ2QsU0FBVTtZQUNSdEIsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxNQUFNd0QsbUJBQW1CLE9BQU9DLFVBQWtCQztRQWNoRCxJQUFJO1lBQ0YsTUFBTTdDLFdBQVcsTUFBTUMsTUFBTSxzQkFBK0IsT0FBVDJDLFdBQVk7Z0JBQzdENUIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUN5QjtZQUN2QjtZQUVBLElBQUk3QyxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2Y1Qix5Q0FBS0EsQ0FBQ2lFLE9BQU8sQ0FBQztnQkFDZGhELGtCQUFrQjtnQkFDbEJJLGtCQUFrQjtnQkFDbEJJLGdCQUFnQixtQkFBbUI7WUFDckMsT0FBTztnQkFDTCxNQUFNYyxZQUFZLE1BQU1iLFNBQVNJLElBQUk7Z0JBQ3JDOUIseUNBQUtBLENBQUNtQyxLQUFLLENBQUNJLFVBQVVKLEtBQUssSUFBSTtZQUNqQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q25DLHlDQUFLQSxDQUFDbUMsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1xQyxxQkFBcUIsT0FBT0Y7UUFDaEMsSUFBSTtZQUNGLE1BQU01QyxXQUFXLE1BQU1DLE1BQU0sc0JBQStCLE9BQVQyQyxXQUFZO2dCQUM3RDVCLFFBQVE7WUFDVjtZQUVBLElBQUloQixTQUFTRSxFQUFFLEVBQUU7Z0JBQ2Y1Qix5Q0FBS0EsQ0FBQ2lFLE9BQU8sQ0FBQztnQkFDZHhDLGdCQUFnQixtQkFBbUI7WUFDckMsT0FBTztnQkFDTCxNQUFNYyxZQUFZLE1BQU1iLFNBQVNJLElBQUk7Z0JBQ3JDOUIseUNBQUtBLENBQUNtQyxLQUFLLENBQUNJLFVBQVVKLEtBQUssSUFBSTtZQUNqQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6Q25DLHlDQUFLQSxDQUFDbUMsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1zQyxrQkFBa0IsQ0FBQ0g7UUFDdkIsTUFBTUksU0FBU2xFLFFBQVFtRSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLEVBQUUsS0FBS1A7UUFDOUMsSUFBSUksUUFBUTtZQUNWckQsa0JBQWtCcUQ7WUFDbEJ2RCxtQkFBbUI7UUFDckI7SUFDRjtJQUVBLE1BQU0yRCxvQkFBb0J4RCxtQkFDeEJBLGlCQUFpQlksWUFBWSxHQUFHWixpQkFBaUJTLFFBQVEsR0FBRztJQUU5RCxJQUFJckIsU0FBUztRQUNYLHFCQUNFLDhEQUFDcUU7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDs7MENBQ0MsOERBQUNFO2dDQUFHRCxXQUFVOzBDQUFxQjs7Ozs7OzBDQUNuQyw4REFBQ0U7Z0NBQUVGLFdBQVU7MENBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNdEMsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNaO3dCQUFDO3dCQUFHO3dCQUFHO3FCQUFFLENBQUNHLEdBQUcsQ0FBQyxDQUFDQyxrQkFDZCw4REFBQ0w7NEJBQVlDLFdBQVU7c0NBQ3JCLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7MkJBSFRJOzs7Ozs7Ozs7Ozs7Ozs7O0lBVXBCO0lBRUEscUJBQ0UsOERBQUNMO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEOzswQ0FDQyw4REFBQ0U7Z0NBQUdELFdBQVU7MENBQXFCOzs7Ozs7MENBQ25DLDhEQUFDRTtnQ0FBRUYsV0FBVTs7b0NBQXFCO2tEQUMyQiw4REFBQ0s7a0RBQVE5RTs7Ozs7O29DQUFvQjs7Ozs7Ozs7Ozs7OztrQ0FJNUYsOERBQUMrRTt3QkFDQ0MsU0FBUyxJQUFNeEUsb0JBQW9CO3dCQUNuQ3lFLFVBQVUsQ0FBQ1Y7d0JBQ1hFLFdBQVU7OzBDQUVWLDhEQUFDcEYsMEdBQUlBO2dDQUFDb0YsV0FBVTs7Ozs7OzRCQUFZOzs7Ozs7Ozs7Ozs7O1lBTS9CMUQsa0NBQ0MsOERBQUN5RDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNsRiwwR0FBV0E7b0NBQUNrRixXQUFVOzs7Ozs7OENBQ3ZCLDhEQUFDUztvQ0FBS1QsV0FBVTs7c0RBQ2QsOERBQUNLO3NEQUFRL0QsaUJBQWlCVyxJQUFJLENBQUN5RCxNQUFNLENBQUMsR0FBR0MsV0FBVyxLQUFLckUsaUJBQWlCVyxJQUFJLENBQUMyRCxLQUFLLENBQUM7Ozs7Ozt3Q0FBWTt3Q0FDaEc7d0NBQUt0RSxpQkFBaUJZLFlBQVk7d0NBQUM7d0NBQUtaLGlCQUFpQlMsUUFBUTt3Q0FBQzs7Ozs7Ozs7Ozs7OztzQ0FHdkUsOERBQUN1RDs0QkFDQ0MsU0FBUzlEOzRCQUNUdUQsV0FBVTs7OENBRVYsOERBQUNqRiwwR0FBU0E7b0NBQUNpRixXQUFVOzs7Ozs7Z0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRN0N4RSxRQUFROEIsTUFBTSxLQUFLLGtCQUNsQiw4REFBQ3lDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNuRiwyR0FBR0E7NEJBQUNtRixXQUFVOzs7Ozs7c0NBQ2YsOERBQUNhOzRCQUFHYixXQUFVO3NDQUE2Qjs7Ozs7O3NDQUMzQyw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQXFCOzs7Ozs7c0NBR2xDLDhEQUFDTTs0QkFDQ0MsU0FBUyxJQUFNeEUsb0JBQW9COzRCQUNuQ3lFLFVBQVUsQ0FBQ1Y7NEJBQ1hFLFdBQVU7OzhDQUVWLDhEQUFDcEYsMEdBQUlBO29DQUFDb0YsV0FBVTs7Ozs7O2dDQUFZOzs7Ozs7Ozs7Ozs7Ozs7OztxQ0FNbEMsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNaeEUsUUFBUTJFLEdBQUcsQ0FBQyxDQUFDVCx1QkFDWiw4REFBQ3pFLG1EQUFVQTt3QkFFVHlFLFFBQVFBO3dCQUNSb0IsUUFBUSxDQUFDbEI7NEJBQ1B2RCxrQkFBa0J1RDs0QkFDbEIzRCxrQkFBa0I7d0JBQ3BCO3dCQUNBOEUsVUFBVXZCO3dCQUNWd0IsYUFBYXZCO3VCQVBSQyxPQUFPRyxFQUFFOzs7Ozs7Ozs7OzBCQWN0Qiw4REFBQzNFLG1FQUFrQkE7Z0JBQ2pCK0YsTUFBTW5GO2dCQUNOb0YsY0FBY25GO2dCQUNkb0YsZ0JBQWdCM0Q7Z0JBQ2hCakMsWUFBWUE7Z0JBQ1pLLFVBQVVBO2dCQUNWd0Ysa0JBQWtCOUUsQ0FBQUEsNkJBQUFBLHVDQUFBQSxpQkFBa0JXLElBQUksS0FBSTs7Ozs7O1lBRzdDYixnQ0FDQzs7a0NBQ0UsOERBQUNqQiwrREFBZ0JBO3dCQUNmOEYsTUFBTWpGO3dCQUNOa0YsY0FBY2pGO3dCQUNkeUQsUUFBUXREO3dCQUNSaUYsZ0JBQWdCLENBQUM5QixVQUFZRixpQkFBaUJqRCxlQUFleUQsRUFBRSxFQUFFTjs7Ozs7O2tDQUduRSw4REFBQ25FLGlFQUFpQkE7d0JBQ2hCNkYsTUFBTS9FO3dCQUNOZ0YsY0FBYy9FO3dCQUNkdUQsUUFBUXREOzs7Ozs7Ozs7Ozs7OztBQU1wQjtHQWpWZ0JmO0tBQUFBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcVXNlckFwaUtleXNcXEFwaUtleU1hbmFnZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBQbHVzLCBLZXksIEFsZXJ0Q2lyY2xlLCBSZWZyZXNoQ3cgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xuaW1wb3J0IHsgQXBpS2V5Q2FyZCB9IGZyb20gJy4vQXBpS2V5Q2FyZCc7XG5pbXBvcnQgeyBDcmVhdGVBcGlLZXlEaWFsb2cgfSBmcm9tICcuL0NyZWF0ZUFwaUtleURpYWxvZyc7XG5pbXBvcnQgeyBFZGl0QXBpS2V5RGlhbG9nIH0gZnJvbSAnLi9FZGl0QXBpS2V5RGlhbG9nJztcbmltcG9ydCB7IEFwaUtleVVzYWdlRGlhbG9nIH0gZnJvbSAnLi9BcGlLZXlVc2FnZURpYWxvZyc7XG5cbmludGVyZmFjZSBBcGlLZXlNYW5hZ2VyUHJvcHMge1xuICBjb25maWdJZDogc3RyaW5nO1xuICBjb25maWdOYW1lOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBcGlLZXlNYW5hZ2VyKHsgY29uZmlnSWQsIGNvbmZpZ05hbWUgfTogQXBpS2V5TWFuYWdlclByb3BzKSB7XG4gIGNvbnN0IFthcGlLZXlzLCBzZXRBcGlLZXlzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbY3JlYXRpbmcsIHNldENyZWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dDcmVhdGVEaWFsb2csIHNldFNob3dDcmVhdGVEaWFsb2ddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0VkaXREaWFsb2csIHNldFNob3dFZGl0RGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dVc2FnZURpYWxvZywgc2V0U2hvd1VzYWdlRGlhbG9nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkQXBpS2V5LCBzZXRTZWxlY3RlZEFwaUtleV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBjb25zdCBbc3Vic2NyaXB0aW9uSW5mbywgc2V0U3Vic2NyaXB0aW9uSW5mb10gPSB1c2VTdGF0ZTx7XG4gICAgdGllcjogc3RyaW5nO1xuICAgIGtleUxpbWl0OiBudW1iZXI7XG4gICAgY3VycmVudENvdW50OiBudW1iZXI7XG4gIH0gfCBudWxsPihudWxsKTtcblxuICAvLyBGZXRjaCBzdWJzY3JpcHRpb24gaW5mbyBhbmQgQVBJIGtleXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFN1YnNjcmlwdGlvbkluZm8oKTtcbiAgICBmZXRjaEFwaUtleXMoKTtcbiAgfSwgW2NvbmZpZ0lkXSk7XG5cbiAgY29uc3QgZmV0Y2hTdWJzY3JpcHRpb25JbmZvID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3N0cmlwZS9zdWJzY3JpcHRpb24tc3RhdHVzJyk7XG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgXG4gICAgICAgIC8vIERldGVybWluZSBrZXkgbGltaXRzIGJhc2VkIG9uIHN1YnNjcmlwdGlvbiB0aWVyXG4gICAgICAgIGxldCBrZXlMaW1pdCA9IDM7IC8vIERlZmF1bHQgZm9yIHN0YXJ0ZXJcbiAgICAgICAgaWYgKGRhdGEuc3Vic2NyaXB0aW9uPy50aWVyID09PSAncHJvZmVzc2lvbmFsJykge1xuICAgICAgICAgIGtleUxpbWl0ID0gMTA7XG4gICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zdWJzY3JpcHRpb24/LnRpZXIgPT09ICdlbnRlcnByaXNlJykge1xuICAgICAgICAgIGtleUxpbWl0ID0gMjU7XG4gICAgICAgIH1cblxuICAgICAgICBzZXRTdWJzY3JpcHRpb25JbmZvKHtcbiAgICAgICAgICB0aWVyOiBkYXRhLnN1YnNjcmlwdGlvbj8udGllciB8fCAnc3RhcnRlcicsXG4gICAgICAgICAga2V5TGltaXQsXG4gICAgICAgICAgY3VycmVudENvdW50OiAwIC8vIFdpbGwgYmUgdXBkYXRlZCB3aGVuIHdlIGZldGNoIEFQSSBrZXlzXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdWJzY3JpcHRpb24gaW5mbzonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoQXBpS2V5cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdXNlci1hcGkta2V5cz9jdXN0b21fY29uZmlnX2lkPSR7Y29uZmlnSWR9YCk7XG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBzZXRBcGlLZXlzKGRhdGEuYXBpS2V5cyB8fCBbXSk7XG4gICAgICAgIFxuICAgICAgICAvLyBVcGRhdGUgc3Vic2NyaXB0aW9uIGluZm8gd2l0aCBjdXJyZW50IGNvdW50XG4gICAgICAgIGlmIChzdWJzY3JpcHRpb25JbmZvKSB7XG4gICAgICAgICAgc2V0U3Vic2NyaXB0aW9uSW5mbyhwcmV2ID0+IHByZXYgPyB7XG4gICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgY3VycmVudENvdW50OiBkYXRhLmFwaUtleXM/Lmxlbmd0aCB8fCAwXG4gICAgICAgICAgfSA6IG51bGwpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRvYXN0LmVycm9yKGVycm9yRGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGZldGNoIEFQSSBrZXlzJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIEFQSSBrZXlzOicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggQVBJIGtleXMnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZUFwaUtleSA9IGFzeW5jIChrZXlEYXRhOiB7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIHBlcm1pc3Npb25zOiB7XG4gICAgICBjaGF0OiBib29sZWFuO1xuICAgICAgc3RyZWFtaW5nOiBib29sZWFuO1xuICAgICAgYWxsX21vZGVsczogYm9vbGVhbjtcbiAgICB9O1xuICAgIHJhdGVMaW1pdHM6IHtcbiAgICAgIHBlck1pbnV0ZTogbnVtYmVyO1xuICAgICAgcGVySG91cjogbnVtYmVyO1xuICAgICAgcGVyRGF5OiBudW1iZXI7XG4gICAgfTtcbiAgICByZXN0cmljdGlvbnM6IHtcbiAgICAgIGFsbG93ZWRJcHM6IHN0cmluZ1tdO1xuICAgICAgYWxsb3dlZERvbWFpbnM6IHN0cmluZ1tdO1xuICAgIH07XG4gICAgZXhwaXJlc0F0Pzogc3RyaW5nO1xuICB9KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldENyZWF0aW5nKHRydWUpO1xuICAgICAgXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3VzZXItYXBpLWtleXMnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGN1c3RvbV9jb25maWdfaWQ6IGNvbmZpZ0lkLFxuICAgICAgICAgIGtleV9uYW1lOiBrZXlEYXRhLm5hbWUsXG4gICAgICAgICAgcGVybWlzc2lvbnM6IGtleURhdGEucGVybWlzc2lvbnMsXG4gICAgICAgICAgcmF0ZV9saW1pdF9wZXJfbWludXRlOiBrZXlEYXRhLnJhdGVMaW1pdHMucGVyTWludXRlLFxuICAgICAgICAgIHJhdGVfbGltaXRfcGVyX2hvdXI6IGtleURhdGEucmF0ZUxpbWl0cy5wZXJIb3VyLFxuICAgICAgICAgIHJhdGVfbGltaXRfcGVyX2RheToga2V5RGF0YS5yYXRlTGltaXRzLnBlckRheSxcbiAgICAgICAgICBhbGxvd2VkX2lwczoga2V5RGF0YS5yZXN0cmljdGlvbnMuYWxsb3dlZElwcyxcbiAgICAgICAgICBhbGxvd2VkX2RvbWFpbnM6IGtleURhdGEucmVzdHJpY3Rpb25zLmFsbG93ZWREb21haW5zLFxuICAgICAgICAgIGV4cGlyZXNfYXQ6IGtleURhdGEuZXhwaXJlc0F0LFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgdG9hc3Quc3VjY2VzcygnQVBJIGtleSBjcmVhdGVkIHN1Y2Nlc3NmdWxseSEnKTtcbiAgICAgICAgXG4gICAgICAgIC8vIFNob3cgdGhlIGdlbmVyYXRlZCBrZXkgdG8gdGhlIHVzZXIgKHRoaXMgaXMgdGhlIG9ubHkgdGltZSB0aGV5J2xsIHNlZSBpdClcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhgWW91ciBBUEkga2V5OiAke2RhdGEuZ2VuZXJhdGVkS2V5fWAsIHtcbiAgICAgICAgICBkdXJhdGlvbjogMTAwMDAsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdTYXZlIHRoaXMga2V5IG5vdyAtIHlvdSB3b25cXCd0IGJlIGFibGUgdG8gc2VlIGl0IGFnYWluISdcbiAgICAgICAgfSk7XG4gICAgICAgIFxuICAgICAgICBzZXRTaG93Q3JlYXRlRGlhbG9nKGZhbHNlKTtcbiAgICAgICAgZmV0Y2hBcGlLZXlzKCk7IC8vIFJlZnJlc2ggdGhlIGxpc3RcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgdG9hc3QuZXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gY3JlYXRlIEFQSSBrZXknKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgQVBJIGtleTonLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBBUEkga2V5Jyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldENyZWF0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRWRpdEFwaUtleSA9IGFzeW5jIChhcGlLZXlJZDogc3RyaW5nLCB1cGRhdGVzOiB7XG4gICAga2V5X25hbWU/OiBzdHJpbmc7XG4gICAgcGVybWlzc2lvbnM/OiB7XG4gICAgICBjaGF0OiBib29sZWFuO1xuICAgICAgc3RyZWFtaW5nOiBib29sZWFuO1xuICAgICAgYWxsX21vZGVsczogYm9vbGVhbjtcbiAgICB9O1xuICAgIHJhdGVfbGltaXRfcGVyX21pbnV0ZT86IG51bWJlcjtcbiAgICByYXRlX2xpbWl0X3Blcl9ob3VyPzogbnVtYmVyO1xuICAgIHJhdGVfbGltaXRfcGVyX2RheT86IG51bWJlcjtcbiAgICBhbGxvd2VkX2lwcz86IHN0cmluZ1tdO1xuICAgIGFsbG93ZWRfZG9tYWlucz86IHN0cmluZ1tdO1xuICAgIHN0YXR1cz86ICdhY3RpdmUnIHwgJ2luYWN0aXZlJztcbiAgfSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3VzZXItYXBpLWtleXMvJHthcGlLZXlJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ1BBVENIJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHVwZGF0ZXMpLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICB0b2FzdC5zdWNjZXNzKCdBUEkga2V5IHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5IScpO1xuICAgICAgICBzZXRTaG93RWRpdERpYWxvZyhmYWxzZSk7XG4gICAgICAgIHNldFNlbGVjdGVkQXBpS2V5KG51bGwpO1xuICAgICAgICBmZXRjaEFwaUtleXMoKTsgLy8gUmVmcmVzaCB0aGUgbGlzdFxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0b2FzdC5lcnJvcihlcnJvckRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byB1cGRhdGUgQVBJIGtleScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBBUEkga2V5OicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIEFQSSBrZXknKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmV2b2tlQXBpS2V5ID0gYXN5bmMgKGFwaUtleUlkOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS91c2VyLWFwaS1rZXlzLyR7YXBpS2V5SWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICB0b2FzdC5zdWNjZXNzKCdBUEkga2V5IHJldm9rZWQgc3VjY2Vzc2Z1bGx5IScpO1xuICAgICAgICBmZXRjaEFwaUtleXMoKTsgLy8gUmVmcmVzaCB0aGUgbGlzdFxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0b2FzdC5lcnJvcihlcnJvckRhdGEuZXJyb3IgfHwgJ0ZhaWxlZCB0byByZXZva2UgQVBJIGtleScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXZva2luZyBBUEkga2V5OicsIGVycm9yKTtcbiAgICAgIHRvYXN0LmVycm9yKCdGYWlsZWQgdG8gcmV2b2tlIEFQSSBrZXknKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVmlld1VzYWdlID0gKGFwaUtleUlkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBhcGlLZXkgPSBhcGlLZXlzLmZpbmQoa2V5ID0+IGtleS5pZCA9PT0gYXBpS2V5SWQpO1xuICAgIGlmIChhcGlLZXkpIHtcbiAgICAgIHNldFNlbGVjdGVkQXBpS2V5KGFwaUtleSk7XG4gICAgICBzZXRTaG93VXNhZ2VEaWFsb2codHJ1ZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNhbkNyZWF0ZU1vcmVLZXlzID0gc3Vic2NyaXB0aW9uSW5mbyA/IFxuICAgIHN1YnNjcmlwdGlvbkluZm8uY3VycmVudENvdW50IDwgc3Vic2NyaXB0aW9uSW5mby5rZXlMaW1pdCA6IGZhbHNlO1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj5BUEkgS2V5IE1hbmFnZW1lbnQ8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC0xXCI+XG4gICAgICAgICAgICAgIENyZWF0ZSBhbmQgbWFuYWdlIEFQSSBrZXlzIGZvciBwcm9ncmFtbWF0aWMgYWNjZXNzIHRvIHlvdXIgUm91S2V5IGNvbmZpZ3VyYXRpb24uXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAge1sxLCAyLCAzXS5tYXAoKGkpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS0yMDAgc2hhZG93LXNtIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTIwMCByb3VuZGVkIHctMS80IG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1ncmF5LTIwMCByb3VuZGVkIHctMS8yXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj5BUEkgS2V5IE1hbmFnZW1lbnQ8L2gyPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgQ3JlYXRlIGFuZCBtYW5hZ2UgQVBJIGtleXMgZm9yIHByb2dyYW1tYXRpYyBhY2Nlc3MgdG8geW91ciA8c3Ryb25nPntjb25maWdOYW1lfTwvc3Ryb25nPiBjb25maWd1cmF0aW9uLlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVEaWFsb2codHJ1ZSl9XG4gICAgICAgICAgZGlzYWJsZWQ9eyFjYW5DcmVhdGVNb3JlS2V5c31cbiAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtNCBweS0yIGJnLW9yYW5nZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLW9yYW5nZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICA+XG4gICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgQ3JlYXRlIEFQSSBLZXlcbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN1YnNjcmlwdGlvbiBJbmZvICovfVxuICAgICAge3N1YnNjcmlwdGlvbkluZm8gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtOTAwXCI+XG4gICAgICAgICAgICAgICAgPHN0cm9uZz57c3Vic2NyaXB0aW9uSW5mby50aWVyLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgc3Vic2NyaXB0aW9uSW5mby50aWVyLnNsaWNlKDEpfTwvc3Ryb25nPiBwbGFuOlxuICAgICAgICAgICAgICAgIHsnICd9e3N1YnNjcmlwdGlvbkluZm8uY3VycmVudENvdW50fSBvZiB7c3Vic2NyaXB0aW9uSW5mby5rZXlMaW1pdH0gQVBJIGtleXMgdXNlZFxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hBcGlLZXlzfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS01MFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIEFQSSBLZXlzIExpc3QgKi99XG4gICAgICB7YXBpS2V5cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHNoYWRvdy1zbVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCBweC02XCI+XG4gICAgICAgICAgICA8S2V5IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBteC1hdXRvIHRleHQtZ3JheS00MDAgbWItNFwiIC8+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTJcIj5ObyBBUEkgS2V5czwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgQ3JlYXRlIHlvdXIgZmlyc3QgQVBJIGtleSB0byBzdGFydCB1c2luZyB0aGUgUm91S2V5IEFQSSBwcm9ncmFtbWF0aWNhbGx5LlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q3JlYXRlRGlhbG9nKHRydWUpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17IWNhbkNyZWF0ZU1vcmVLZXlzfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtNCBweS0yIGJnLW9yYW5nZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLW9yYW5nZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgQ3JlYXRlIFlvdXIgRmlyc3QgQVBJIEtleVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSA6IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAge2FwaUtleXMubWFwKChhcGlLZXkpID0+IChcbiAgICAgICAgICAgIDxBcGlLZXlDYXJkXG4gICAgICAgICAgICAgIGtleT17YXBpS2V5LmlkfVxuICAgICAgICAgICAgICBhcGlLZXk9e2FwaUtleX1cbiAgICAgICAgICAgICAgb25FZGl0PXsoa2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRBcGlLZXkoa2V5KTtcbiAgICAgICAgICAgICAgICBzZXRTaG93RWRpdERpYWxvZyh0cnVlKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25SZXZva2U9e2hhbmRsZVJldm9rZUFwaUtleX1cbiAgICAgICAgICAgICAgb25WaWV3VXNhZ2U9e2hhbmRsZVZpZXdVc2FnZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIERpYWxvZ3MgKi99XG4gICAgICA8Q3JlYXRlQXBpS2V5RGlhbG9nXG4gICAgICAgIG9wZW49e3Nob3dDcmVhdGVEaWFsb2d9XG4gICAgICAgIG9uT3BlbkNoYW5nZT17c2V0U2hvd0NyZWF0ZURpYWxvZ31cbiAgICAgICAgb25DcmVhdGVBcGlLZXk9e2hhbmRsZUNyZWF0ZUFwaUtleX1cbiAgICAgICAgY29uZmlnTmFtZT17Y29uZmlnTmFtZX1cbiAgICAgICAgY3JlYXRpbmc9e2NyZWF0aW5nfVxuICAgICAgICBzdWJzY3JpcHRpb25UaWVyPXtzdWJzY3JpcHRpb25JbmZvPy50aWVyIHx8ICdzdGFydGVyJ31cbiAgICAgIC8+XG5cbiAgICAgIHtzZWxlY3RlZEFwaUtleSAmJiAoXG4gICAgICAgIDw+XG4gICAgICAgICAgPEVkaXRBcGlLZXlEaWFsb2dcbiAgICAgICAgICAgIG9wZW49e3Nob3dFZGl0RGlhbG9nfVxuICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRTaG93RWRpdERpYWxvZ31cbiAgICAgICAgICAgIGFwaUtleT17c2VsZWN0ZWRBcGlLZXl9XG4gICAgICAgICAgICBvblVwZGF0ZUFwaUtleT17KHVwZGF0ZXMpID0+IGhhbmRsZUVkaXRBcGlLZXkoc2VsZWN0ZWRBcGlLZXkuaWQsIHVwZGF0ZXMpfVxuICAgICAgICAgIC8+XG5cbiAgICAgICAgICA8QXBpS2V5VXNhZ2VEaWFsb2dcbiAgICAgICAgICAgIG9wZW49e3Nob3dVc2FnZURpYWxvZ31cbiAgICAgICAgICAgIG9uT3BlbkNoYW5nZT17c2V0U2hvd1VzYWdlRGlhbG9nfVxuICAgICAgICAgICAgYXBpS2V5PXtzZWxlY3RlZEFwaUtleX1cbiAgICAgICAgICAvPlxuICAgICAgICA8Lz5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlBsdXMiLCJLZXkiLCJBbGVydENpcmNsZSIsIlJlZnJlc2hDdyIsInRvYXN0IiwiQXBpS2V5Q2FyZCIsIkNyZWF0ZUFwaUtleURpYWxvZyIsIkVkaXRBcGlLZXlEaWFsb2ciLCJBcGlLZXlVc2FnZURpYWxvZyIsIkFwaUtleU1hbmFnZXIiLCJjb25maWdJZCIsImNvbmZpZ05hbWUiLCJhcGlLZXlzIiwic2V0QXBpS2V5cyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiY3JlYXRpbmciLCJzZXRDcmVhdGluZyIsInNob3dDcmVhdGVEaWFsb2ciLCJzZXRTaG93Q3JlYXRlRGlhbG9nIiwic2hvd0VkaXREaWFsb2ciLCJzZXRTaG93RWRpdERpYWxvZyIsInNob3dVc2FnZURpYWxvZyIsInNldFNob3dVc2FnZURpYWxvZyIsInNlbGVjdGVkQXBpS2V5Iiwic2V0U2VsZWN0ZWRBcGlLZXkiLCJzdWJzY3JpcHRpb25JbmZvIiwic2V0U3Vic2NyaXB0aW9uSW5mbyIsImZldGNoU3Vic2NyaXB0aW9uSW5mbyIsImZldGNoQXBpS2V5cyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImRhdGEiLCJqc29uIiwia2V5TGltaXQiLCJzdWJzY3JpcHRpb24iLCJ0aWVyIiwiY3VycmVudENvdW50IiwiZXJyb3IiLCJjb25zb2xlIiwicHJldiIsImxlbmd0aCIsImVycm9yRGF0YSIsImhhbmRsZUNyZWF0ZUFwaUtleSIsImtleURhdGEiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJjdXN0b21fY29uZmlnX2lkIiwia2V5X25hbWUiLCJuYW1lIiwicGVybWlzc2lvbnMiLCJyYXRlX2xpbWl0X3Blcl9taW51dGUiLCJyYXRlTGltaXRzIiwicGVyTWludXRlIiwicmF0ZV9saW1pdF9wZXJfaG91ciIsInBlckhvdXIiLCJyYXRlX2xpbWl0X3Blcl9kYXkiLCJwZXJEYXkiLCJhbGxvd2VkX2lwcyIsInJlc3RyaWN0aW9ucyIsImFsbG93ZWRJcHMiLCJhbGxvd2VkX2RvbWFpbnMiLCJhbGxvd2VkRG9tYWlucyIsImV4cGlyZXNfYXQiLCJleHBpcmVzQXQiLCJzdWNjZXNzIiwiZ2VuZXJhdGVkS2V5IiwiZHVyYXRpb24iLCJkZXNjcmlwdGlvbiIsImhhbmRsZUVkaXRBcGlLZXkiLCJhcGlLZXlJZCIsInVwZGF0ZXMiLCJoYW5kbGVSZXZva2VBcGlLZXkiLCJoYW5kbGVWaWV3VXNhZ2UiLCJhcGlLZXkiLCJmaW5kIiwia2V5IiwiaWQiLCJjYW5DcmVhdGVNb3JlS2V5cyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwicCIsIm1hcCIsImkiLCJzdHJvbmciLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJzcGFuIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsImgzIiwib25FZGl0Iiwib25SZXZva2UiLCJvblZpZXdVc2FnZSIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJvbkNyZWF0ZUFwaUtleSIsInN1YnNjcmlwdGlvblRpZXIiLCJvblVwZGF0ZUFwaUtleSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});