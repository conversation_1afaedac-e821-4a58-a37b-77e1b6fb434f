"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx":
/*!***********************************************************!*\
  !*** ./src/components/UserApiKeys/CreateApiKeyDialog.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateApiKeyDialog: () => (/* binding */ CreateApiKeyDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Eye,EyeOff,Globe,Key,Plus,Shield,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ CreateApiKeyDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction CreateApiKeyDialog(param) {\n    let { open, onOpenChange, onCreateApiKey, configName, creating, subscriptionTier } = param;\n    _s();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('form');\n    const [createdApiKey, setCreatedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showFullKey, setShowFullKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key_name: '',\n        permissions: {\n            chat: true,\n            streaming: true,\n            all_models: true\n        },\n        rate_limit_per_minute: getDefaultRateLimits(subscriptionTier).per_minute,\n        rate_limit_per_hour: getDefaultRateLimits(subscriptionTier).per_hour,\n        rate_limit_per_day: getDefaultRateLimits(subscriptionTier).per_day,\n        allowed_ips: [],\n        allowed_domains: [],\n        expires_at: ''\n    });\n    const [ipInput, setIpInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [domainInput, setDomainInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    function getDefaultRateLimits(tier) {\n        const limits = {\n            starter: {\n                per_minute: 30,\n                per_hour: 500,\n                per_day: 5000\n            },\n            professional: {\n                per_minute: 100,\n                per_hour: 2000,\n                per_day: 20000\n            },\n            enterprise: {\n                per_minute: 300,\n                per_hour: 10000,\n                per_day: 100000\n            }\n        };\n        return limits[tier] || limits.starter;\n    }\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.key_name.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Please enter a name for your API key');\n            return;\n        }\n        try {\n            const result = await onCreateApiKey({\n                ...formData,\n                key_name: formData.key_name.trim(),\n                expires_at: formData.expires_at || undefined\n            });\n            setCreatedApiKey(result);\n            setStep('success');\n        } catch (error) {\n        // Error is handled in the parent component\n        }\n    };\n    const addIpAddress = ()=>{\n        if (ipInput.trim() && !formData.allowed_ips.includes(ipInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_ips: [\n                        ...prev.allowed_ips,\n                        ipInput.trim()\n                    ]\n                }));\n            setIpInput('');\n        }\n    };\n    const removeIpAddress = (ip)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_ips: prev.allowed_ips.filter((i)=>i !== ip)\n            }));\n    };\n    const addDomain = ()=>{\n        if (domainInput.trim() && !formData.allowed_domains.includes(domainInput.trim())) {\n            setFormData((prev)=>({\n                    ...prev,\n                    allowed_domains: [\n                        ...prev.allowed_domains,\n                        domainInput.trim()\n                    ]\n                }));\n            setDomainInput('');\n        }\n    };\n    const removeDomain = (domain)=>{\n        setFormData((prev)=>({\n                ...prev,\n                allowed_domains: prev.allowed_domains.filter((d)=>d !== domain)\n            }));\n    };\n    const copyApiKey = async ()=>{\n        if (createdApiKey === null || createdApiKey === void 0 ? void 0 : createdApiKey.api_key) {\n            try {\n                await navigator.clipboard.writeText(createdApiKey.api_key);\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key copied to clipboard');\n            } catch (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to copy API key');\n            }\n        }\n    };\n    const handleClose = ()=>{\n        setStep('form');\n        setCreatedApiKey(null);\n        setShowFullKey(false);\n        setFormData({\n            key_name: '',\n            permissions: {\n                chat: true,\n                streaming: true,\n                all_models: true\n            },\n            rate_limit_per_minute: getDefaultRateLimits(subscriptionTier).per_minute,\n            rate_limit_per_hour: getDefaultRateLimits(subscriptionTier).per_hour,\n            rate_limit_per_day: getDefaultRateLimits(subscriptionTier).per_day,\n            allowed_ips: [],\n            allowed_domains: [],\n            expires_at: ''\n        });\n        setIpInput('');\n        setDomainInput('');\n        onOpenChange(false);\n    };\n    if (step === 'success' && createdApiKey) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n            open: open,\n            onOpenChange: handleClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n                className: \"max-w-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-5 w-5 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"API Key Created Successfully\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogDescription, {\n                                children: \"Your API key has been created. Make sure to copy it now as you won't be able to see it again.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                                className: \"border-amber-200 bg-amber-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-amber-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertDescription, {\n                                        className: \"text-amber-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Important:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" This is the only time you'll see the full API key. Make sure to copy and store it securely.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        children: \"API Key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"flex-1 text-sm font-mono\",\n                                                children: showFullKey ? createdApiKey.api_key : \"\".concat(createdApiKey.key_prefix, \"_\").concat('*'.repeat(32))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>setShowFullKey(!showFullKey),\n                                                className: \"h-8 w-8 p-0\",\n                                                children: showFullKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 34\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 67\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: copyApiKey,\n                                                className: \"h-8 w-8 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                children: \"Key Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: createdApiKey.key_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                children: \"Created\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: new Date(createdApiKey.created_at).toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        children: \"Rate Limits\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-2 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-2 bg-blue-50 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-blue-900\",\n                                                        children: createdApiKey.rate_limits.per_minute\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-700\",\n                                                        children: \"per minute\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-2 bg-blue-50 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-blue-900\",\n                                                        children: createdApiKey.rate_limits.per_hour\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-700\",\n                                                        children: \"per hour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-2 bg-blue-50 rounded\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold text-blue-900\",\n                                                        children: createdApiKey.rate_limits.per_day\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-700\",\n                                                        children: \"per day\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogFooter, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                            onClick: handleClose,\n                            className: \"w-full\",\n                            children: \"Done\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dialog, {\n        open: open,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                \"Create API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogDescription, {\n                            children: [\n                                \"Create a new API key for programmatic access to \",\n                                configName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        htmlFor: \"key_name\",\n                                        children: \"API Key Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                        id: \"key_name\",\n                                        value: formData.key_name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    key_name: e.target.value\n                                                })),\n                                        placeholder: \"e.g., Production API Key\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"A descriptive name to help you identify this API key\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Checkbox, {\n                                                    id: \"chat\",\n                                                    checked: formData.permissions.chat,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    chat: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"chat\",\n                                                    className: \"text-sm\",\n                                                    children: \"Chat Completions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: \"Required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Checkbox, {\n                                                    id: \"streaming\",\n                                                    checked: formData.permissions.streaming,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    streaming: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"streaming\",\n                                                    className: \"text-sm\",\n                                                    children: \"Streaming Responses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Checkbox, {\n                                                    id: \"all_models\",\n                                                    checked: formData.permissions.all_models,\n                                                    onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                permissions: {\n                                                                    ...prev.permissions,\n                                                                    all_models: !!checked\n                                                                }\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"all_models\",\n                                                    className: \"text-sm\",\n                                                    children: \"Access to All Models\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Rate Limits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                subscriptionTier,\n                                                \" plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"rate_minute\",\n                                                    children: \"Per Minute\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    id: \"rate_minute\",\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"1000\",\n                                                    value: formData.rate_limit_per_minute,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                rate_limit_per_minute: parseInt(e.target.value) || 0\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"rate_hour\",\n                                                    children: \"Per Hour\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    id: \"rate_hour\",\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"50000\",\n                                                    value: formData.rate_limit_per_hour,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                rate_limit_per_hour: parseInt(e.target.value) || 0\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                                    htmlFor: \"rate_day\",\n                                                    children: \"Per Day\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    id: \"rate_day\",\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"500000\",\n                                                    value: formData.rate_limit_per_day,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                rate_limit_per_day: parseInt(e.target.value) || 0\n                                                            }))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            className: \"text-base font-semibold\",\n                                            children: \"Security Restrictions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: \"Optional\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            children: \"Allowed IP Addresses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    value: ipInput,\n                                                    onChange: (e)=>setIpInput(e.target.value),\n                                                    placeholder: \"e.g., *********** or 10.0.0.0/8\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addIpAddress())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    type: \"button\",\n                                                    onClick: addIpAddress,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_ips.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_ips.map((ip)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        ip,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeIpAddress(ip),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, ip, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: \"Leave empty to allow all IP addresses\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                            children: \"Allowed Domains (CORS)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                                    value: domainInput,\n                                                    onChange: (e)=>setDomainInput(e.target.value),\n                                                    placeholder: \"e.g., example.com or *.example.com\",\n                                                    onKeyPress: (e)=>e.key === 'Enter' && (e.preventDefault(), addDomain())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                    type: \"button\",\n                                                    onClick: addDomain,\n                                                    size: \"sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.allowed_domains.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: formData.allowed_domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3 w-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        domain,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                                            type: \"button\",\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeDomain(domain),\n                                                            className: \"ml-1 h-3 w-3 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Eye_EyeOff_Globe_Key_Plus_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-2 w-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, domain, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: \"Leave empty to allow all domains\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Separator, {}, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Label, {\n                                        htmlFor: \"expires_at\",\n                                        children: \"Expiration Date (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Input, {\n                                        id: \"expires_at\",\n                                        type: \"datetime-local\",\n                                        value: formData.expires_at,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    expires_at: e.target.value\n                                                })),\n                                        min: new Date().toISOString().slice(0, 16)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: \"Leave empty for no expiration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: handleClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                    type: \"submit\",\n                                    disabled: creating,\n                                    children: creating ? 'Creating...' : 'Create API Key'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n            lineNumber: 247,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateApiKeyDialog, \"n9/TBNJTv+gMtn6qt6DRS25OIUQ=\");\n_c = CreateApiKeyDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateApiKeyDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\n"));

/***/ })

});