"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx":
/*!***********************************************************!*\
  !*** ./src/components/UserApiKeys/CreateApiKeyDialog.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateApiKeyDialog: () => (/* binding */ CreateApiKeyDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ CreateApiKeyDialog auto */ \nvar _s = $RefreshSig$();\n\nfunction CreateApiKeyDialog(param) {\n    let { open, onOpenChange, onCreateApiKey, configName, creating, subscriptionTier } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key_name: '',\n        permissions: {\n            chat: true,\n            streaming: true,\n            all_models: true\n        },\n        rate_limit_per_minute: 30,\n        rate_limit_per_hour: 500,\n        rate_limit_per_day: 5000,\n        allowed_ips: [],\n        allowed_domains: [],\n        expires_at: ''\n    });\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.key_name.trim()) {\n            alert('Please enter a name for your API key');\n            return;\n        }\n        try {\n            await onCreateApiKey({\n                ...formData,\n                key_name: formData.key_name.trim(),\n                expires_at: formData.expires_at || undefined\n            });\n            onOpenChange(false);\n        } catch (error) {\n        // Error is handled in the parent component\n        }\n    };\n    if (!open) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold mb-4\",\n                        children: \"Create API Key\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: [\n                            \"Create a new API key for \",\n                            configName\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"API Key Name *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.key_name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    key_name: e.target.value\n                                                })),\n                                        placeholder: \"e.g., Production API Key\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>onOpenChange(false),\n                                        className: \"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: creating,\n                                        className: \"px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50\",\n                                        children: creating ? 'Creating...' : 'Create API Key'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\CreateApiKeyDialog.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateApiKeyDialog, \"28GqZBRRZzCoUYzdhzO6jDpwRjw=\");\n_c = CreateApiKeyDialog;\nvar _c;\n$RefreshReg$(_c, \"CreateApiKeyDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\n"));

/***/ })

});