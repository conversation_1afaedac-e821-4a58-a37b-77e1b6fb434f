"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx":
/*!******************************************************!*\
  !*** ./src/components/UserApiKeys/ApiKeyManager.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyManager: () => (/* binding */ ApiKeyManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Key,Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _ApiKeyCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ApiKeyCard */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyCard.tsx\");\n/* harmony import */ var _CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/CreateApiKeyDialog.tsx\");\n/* harmony import */ var _EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EditApiKeyDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/EditApiKeyDialog.tsx\");\n/* harmony import */ var _ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ApiKeyUsageDialog */ \"(app-pages-browser)/./src/components/UserApiKeys/ApiKeyUsageDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ ApiKeyManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ApiKeyManager(param) {\n    let { configId, configName } = param;\n    _s();\n    const [apiKeys, setApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [creating, setCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditDialog, setShowEditDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUsageDialog, setShowUsageDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedApiKey, setSelectedApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscriptionInfo, setSubscriptionInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch subscription info and API keys\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ApiKeyManager.useEffect\": ()=>{\n            fetchSubscriptionInfo();\n            fetchApiKeys();\n        }\n    }[\"ApiKeyManager.useEffect\"], [\n        configId\n    ]);\n    const fetchSubscriptionInfo = async ()=>{\n        try {\n            const response = await fetch('/api/stripe/subscription-status');\n            if (response.ok) {\n                var _data_subscription, _data_subscription1, _data_subscription2;\n                const data = await response.json();\n                // Determine key limits based on subscription tier\n                let keyLimit = 3; // Default for starter\n                if (((_data_subscription = data.subscription) === null || _data_subscription === void 0 ? void 0 : _data_subscription.tier) === 'professional') {\n                    keyLimit = 10;\n                } else if (((_data_subscription1 = data.subscription) === null || _data_subscription1 === void 0 ? void 0 : _data_subscription1.tier) === 'enterprise') {\n                    keyLimit = 25;\n                }\n                setSubscriptionInfo({\n                    tier: ((_data_subscription2 = data.subscription) === null || _data_subscription2 === void 0 ? void 0 : _data_subscription2.tier) || 'starter',\n                    keyLimit,\n                    currentCount: 0 // Will be updated when we fetch API keys\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching subscription info:', error);\n        }\n    };\n    const fetchApiKeys = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/user-api-keys?custom_config_id=\".concat(configId));\n            if (response.ok) {\n                const data = await response.json();\n                setApiKeys(data.apiKeys || []);\n                // Update subscription info with current count\n                if (subscriptionInfo) {\n                    setSubscriptionInfo((prev)=>{\n                        var _data_apiKeys;\n                        return prev ? {\n                            ...prev,\n                            currentCount: ((_data_apiKeys = data.apiKeys) === null || _data_apiKeys === void 0 ? void 0 : _data_apiKeys.length) || 0\n                        } : null;\n                    });\n                }\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to fetch API keys');\n            }\n        } catch (error) {\n            console.error('Error fetching API keys:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to fetch API keys');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateApiKey = async (keyData)=>{\n        try {\n            setCreating(true);\n            const response = await fetch('/api/user-api-keys', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    custom_config_id: configId,\n                    key_name: keyData.name,\n                    permissions: keyData.permissions,\n                    rate_limit_per_minute: keyData.rateLimits.perMinute,\n                    rate_limit_per_hour: keyData.rateLimits.perHour,\n                    rate_limit_per_day: keyData.rateLimits.perDay,\n                    allowed_ips: keyData.restrictions.allowedIps,\n                    allowed_domains: keyData.restrictions.allowedDomains,\n                    expires_at: keyData.expiresAt\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key created successfully!');\n                // Show the generated key to the user (this is the only time they'll see it)\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Your API key: \".concat(data.generatedKey), {\n                    duration: 10000,\n                    description: 'Save this key now - you won\\'t be able to see it again!'\n                });\n                setShowCreateDialog(false);\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to create API key');\n            }\n        } catch (error) {\n            console.error('Error creating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to create API key');\n        } finally{\n            setCreating(false);\n        }\n    };\n    const handleEditApiKey = async (apiKeyId, updates)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(apiKeyId), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(updates)\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key updated successfully!');\n                setShowEditDialog(false);\n                setSelectedApiKey(null);\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to update API key');\n            }\n        } catch (error) {\n            console.error('Error updating API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to update API key');\n        }\n    };\n    const handleRevokeApiKey = async (apiKeyId)=>{\n        try {\n            const response = await fetch(\"/api/user-api-keys/\".concat(apiKeyId), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.success('API key revoked successfully!');\n                fetchApiKeys(); // Refresh the list\n            } else {\n                const errorData = await response.json();\n                sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error(errorData.error || 'Failed to revoke API key');\n            }\n        } catch (error) {\n            console.error('Error revoking API key:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_2__.toast.error('Failed to revoke API key');\n        }\n    };\n    const handleViewUsage = (apiKeyId)=>{\n        const apiKey = apiKeys.find((key)=>key.id === apiKeyId);\n        if (apiKey) {\n            setSelectedApiKey(apiKey);\n            setShowUsageDialog(true);\n        }\n    };\n    const canCreateMoreKeys = subscriptionInfo ? subscriptionInfo.currentCount < subscriptionInfo.keyLimit : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"API Key Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Create and manage API keys for programmatic access to your RouKey configuration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4\",\n                    children: [\n                        1,\n                        2,\n                        3\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                            className: \"animate-pulse\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-200 rounded w-1/4 mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-200 rounded w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        }, i, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"API Key Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: [\n                                    \"Create and manage API keys for programmatic access to your \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: configName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 72\n                                    }, this),\n                                    \" configuration.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCreateDialog(true),\n                        disabled: !canCreateMoreKeys,\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            \"Create API Key\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            subscriptionInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-900\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: subscriptionInfo.tier.charAt(0).toUpperCase() + subscriptionInfo.tier.slice(1)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" plan:\",\n                                        ' ',\n                                        subscriptionInfo.currentCount,\n                                        \" of \",\n                                        subscriptionInfo.keyLimit,\n                                        \" API keys used\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchApiKeys,\n                            className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                \"Refresh\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, this),\n            apiKeys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"No API Keys\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Create your first API key to start using the RouKey API programmatically.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                            onClick: ()=>setShowCreateDialog(true),\n                            disabled: !canCreateMoreKeys,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Key_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this),\n                                \"Create Your First API Key\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4\",\n                children: apiKeys.map((apiKey)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyCard__WEBPACK_IMPORTED_MODULE_3__.ApiKeyCard, {\n                        apiKey: apiKey,\n                        onEdit: (key)=>{\n                            setSelectedApiKey(key);\n                            setShowEditDialog(true);\n                        },\n                        onRevoke: handleRevokeApiKey,\n                        onViewUsage: handleViewUsage\n                    }, apiKey.id, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateApiKeyDialog__WEBPACK_IMPORTED_MODULE_4__.CreateApiKeyDialog, {\n                open: showCreateDialog,\n                onOpenChange: setShowCreateDialog,\n                onCreateApiKey: handleCreateApiKey,\n                configName: configName,\n                creating: creating,\n                subscriptionTier: (subscriptionInfo === null || subscriptionInfo === void 0 ? void 0 : subscriptionInfo.tier) || 'starter'\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            selectedApiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditApiKeyDialog__WEBPACK_IMPORTED_MODULE_5__.EditApiKeyDialog, {\n                        open: showEditDialog,\n                        onOpenChange: setShowEditDialog,\n                        apiKey: selectedApiKey,\n                        onUpdateApiKey: (updates)=>handleEditApiKey(selectedApiKey.id, updates)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ApiKeyUsageDialog__WEBPACK_IMPORTED_MODULE_6__.ApiKeyUsageDialog, {\n                        open: showUsageDialog,\n                        onOpenChange: setShowUsageDialog,\n                        apiKey: selectedApiKey\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\UserApiKeys\\\\ApiKeyManager.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiKeyManager, \"Uxp2HmcmoyLM/7rsP3ld9j5jvUo=\");\n_c = ApiKeyManager;\nvar _c;\n$RefreshReg$(_c, \"ApiKeyManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UserApiKeys/ApiKeyManager.tsx\n"));

/***/ })

});