'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, Key, AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { ApiKeyCard } from './ApiKeyCard';
import { CreateApiKeyDialog } from './CreateApiKeyDialog';
import { EditApiKeyDialog } from './EditApiKeyDialog';
import { ApiKeyUsageDialog } from './ApiKeyUsageDialog';

interface ApiKeyManagerProps {
  configId: string;
  configName: string;
}

export function ApiKeyManager({ configId, configName }: ApiKeyManagerProps) {
  const [apiKeys, setApiKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showUsageDialog, setShowUsageDialog] = useState(false);
  const [selectedApiKey, setSelectedApiKey] = useState<any>(null);
  const [subscriptionInfo, setSubscriptionInfo] = useState<{
    tier: string;
    keyLimit: number;
    currentCount: number;
  } | null>(null);

  // Fetch subscription info and API keys
  useEffect(() => {
    fetchSubscriptionInfo();
    fetchApiKeys();
  }, [configId]);

  const fetchSubscriptionInfo = async () => {
    try {
      const response = await fetch('/api/stripe/subscription-status');
      if (response.ok) {
        const data = await response.json();
        
        // Determine key limits based on subscription tier
        let keyLimit = 3; // Default for starter
        if (data.subscription?.tier === 'professional') {
          keyLimit = 10;
        } else if (data.subscription?.tier === 'enterprise') {
          keyLimit = 25;
        }

        setSubscriptionInfo({
          tier: data.subscription?.tier || 'starter',
          keyLimit,
          currentCount: 0 // Will be updated when we fetch API keys
        });
      }
    } catch (error) {
      console.error('Error fetching subscription info:', error);
    }
  };

  const fetchApiKeys = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/user-api-keys?custom_config_id=${configId}`);
      
      if (response.ok) {
        const data = await response.json();
        setApiKeys(data.apiKeys || []);
        
        // Update subscription info with current count
        if (subscriptionInfo) {
          setSubscriptionInfo(prev => prev ? {
            ...prev,
            currentCount: data.apiKeys?.length || 0
          } : null);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to fetch API keys');
      }
    } catch (error) {
      console.error('Error fetching API keys:', error);
      toast.error('Failed to fetch API keys');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateApiKey = async (keyData: {
    name: string;
    permissions: {
      chat: boolean;
      streaming: boolean;
      all_models: boolean;
    };
    rateLimits: {
      perMinute: number;
      perHour: number;
      perDay: number;
    };
    restrictions: {
      allowedIps: string[];
      allowedDomains: string[];
    };
    expiresAt?: string;
  }) => {
    try {
      setCreating(true);
      
      const response = await fetch('/api/user-api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          custom_config_id: configId,
          key_name: keyData.name,
          permissions: keyData.permissions,
          rate_limit_per_minute: keyData.rateLimits.perMinute,
          rate_limit_per_hour: keyData.rateLimits.perHour,
          rate_limit_per_day: keyData.rateLimits.perDay,
          allowed_ips: keyData.restrictions.allowedIps,
          allowed_domains: keyData.restrictions.allowedDomains,
          expires_at: keyData.expiresAt,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('API key created successfully!');
        
        // Show the generated key to the user (this is the only time they'll see it)
        toast.success(`Your API key: ${data.generatedKey}`, {
          duration: 10000,
          description: 'Save this key now - you won\'t be able to see it again!'
        });
        
        setShowCreateDialog(false);
        fetchApiKeys(); // Refresh the list
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to create API key');
      }
    } catch (error) {
      console.error('Error creating API key:', error);
      toast.error('Failed to create API key');
    } finally {
      setCreating(false);
    }
  };

  const handleEditApiKey = async (apiKeyId: string, updates: {
    key_name?: string;
    permissions?: {
      chat: boolean;
      streaming: boolean;
      all_models: boolean;
    };
    rate_limit_per_minute?: number;
    rate_limit_per_hour?: number;
    rate_limit_per_day?: number;
    allowed_ips?: string[];
    allowed_domains?: string[];
    status?: 'active' | 'inactive';
  }) => {
    try {
      const response = await fetch(`/api/user-api-keys/${apiKeyId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (response.ok) {
        toast.success('API key updated successfully!');
        setShowEditDialog(false);
        setSelectedApiKey(null);
        fetchApiKeys(); // Refresh the list
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to update API key');
      }
    } catch (error) {
      console.error('Error updating API key:', error);
      toast.error('Failed to update API key');
    }
  };

  const handleRevokeApiKey = async (apiKeyId: string) => {
    try {
      const response = await fetch(`/api/user-api-keys/${apiKeyId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('API key revoked successfully!');
        fetchApiKeys(); // Refresh the list
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to revoke API key');
      }
    } catch (error) {
      console.error('Error revoking API key:', error);
      toast.error('Failed to revoke API key');
    }
  };

  const handleViewUsage = (apiKeyId: string) => {
    const apiKey = apiKeys.find(key => key.id === apiKeyId);
    if (apiKey) {
      setSelectedApiKey(apiKey);
      setShowUsageDialog(true);
    }
  };

  const canCreateMoreKeys = subscriptionInfo ? 
    subscriptionInfo.currentCount < subscriptionInfo.keyLimit : false;

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading API keys...
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">API Key Management</h2>
          <p className="text-gray-600 mt-1">
            Create and manage API keys for programmatic access to your <strong>{configName}</strong> configuration.
          </p>
        </div>

        <Button
          onClick={() => setShowCreateDialog(true)}
          disabled={!canCreateMoreKeys}
          variant="primary"
        >
          <Plus className="h-4 w-4" />
          Create API Key
        </Button>
      </div>

      {/* Subscription Info */}
      {subscriptionInfo && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>
                <Badge variant="secondary">{subscriptionInfo.tier.charAt(0).toUpperCase() + subscriptionInfo.tier.slice(1)}</Badge> plan:
                {' '}{subscriptionInfo.currentCount} of {subscriptionInfo.keyLimit} API keys used
              </span>
              <Button
                onClick={fetchApiKeys}
                variant="outline"
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* API Keys List */}
      {apiKeys.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Key className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No API Keys</h3>
            <p className="text-gray-600 mb-4">
              Create your first API key to start using the RouKey API programmatically.
            </p>
            <Button
              onClick={() => setShowCreateDialog(true)}
              disabled={!canCreateMoreKeys}
              variant="primary"
            >
              <Plus className="h-4 w-4" />
              Create Your First API Key
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {apiKeys.map((apiKey) => (
            <ApiKeyCard
              key={apiKey.id}
              apiKey={apiKey}
              onEdit={(key) => {
                setSelectedApiKey(key);
                setShowEditDialog(true);
              }}
              onRevoke={handleRevokeApiKey}
              onViewUsage={handleViewUsage}
            />
          ))}
        </div>
      )}

      {/* Dialogs */}
      <CreateApiKeyDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onCreateApiKey={handleCreateApiKey}
        configName={configName}
        creating={creating}
        subscriptionTier={subscriptionInfo?.tier || 'starter'}
      />

      {selectedApiKey && (
        <>
          <EditApiKeyDialog
            open={showEditDialog}
            onOpenChange={setShowEditDialog}
            apiKey={selectedApiKey}
            onUpdateApiKey={(updates) => handleEditApiKey(selectedApiKey.id, updates)}
          />

          <ApiKeyUsageDialog
            open={showUsageDialog}
            onOpenChange={setShowUsageDialog}
            apiKey={selectedApiKey}
          />
        </>
      )}
    </div>
  );
}
